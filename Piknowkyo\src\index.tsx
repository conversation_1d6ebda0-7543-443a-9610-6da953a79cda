import React from 'react';
import ReactDOM from 'react-dom/client';
import { Provider } from 'react-redux';
import { PersistGate } from 'redux-persist/integration/react';
import './index.css';

import App from './App';
import reportWebVitals from './reportWebVitals';
import { ThemeProvider } from './ThemeProvider';
import { LangProvider } from './LangProvider';
import GlobalStyles from './GlobalStyles';
import { store, persistor } from './store/index';
import { serviceWorkerManager } from './services/serviceWorkerManager';
import './i18n';

const root = ReactDOM.createRoot(
  document.getElementById('root') as HTMLElement
);

root.render(
  <React.StrictMode>
    <Provider store={store}>
      <PersistGate loading={<div>Loading...</div>} persistor={persistor}>
        <LangProvider>
          <ThemeProvider>
            <GlobalStyles />
            <App />
          </ThemeProvider>
        </LangProvider>
      </PersistGate>
    </Provider>
  </React.StrictMode>
);

// Register service worker for offline support and caching
if (process.env.NODE_ENV === 'production') {
  serviceWorkerManager.register().then((registered) => {
    if (registered) {
      console.log('Service worker registered successfully');
    } else {
      console.warn('Service worker registration failed');
    }
  });
} else {
  console.log('Service worker registration skipped in development mode');
}

reportWebVitals();
