import React from 'react';
import ReactDOM from 'react-dom/client';
import { Provider } from 'react-redux';
import { PersistGate } from 'redux-persist/integration/react';
import './index.css';

import App from './App';
import reportWebVitals from './reportWebVitals';
import { ThemeProvider } from './ThemeProvider';
import { LangProvider } from './LangProvider';
import GlobalStyles from './GlobalStyles';
import { store, persistor } from './store/index';
import './i18n';

const root = ReactDOM.createRoot(
  document.getElementById('root') as HTMLElement
);

root.render(
  <React.StrictMode>
    <Provider store={store}>
      <PersistGate loading={<div>Loading...</div>} persistor={persistor}>
        <LangProvider>
          <ThemeProvider>
            <GlobalStyles />
            <App />
          </ThemeProvider>
        </LangProvider>
      </PersistGate>
    </Provider>
  </React.StrictMode>
);

reportWebVitals();
