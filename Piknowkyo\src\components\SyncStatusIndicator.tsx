import React from 'react';
import styled from 'styled-components';
import { useTranslation } from 'react-i18next';
import { FiW<PERSON><PERSON>, FiWifiOff, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, FiAlertCircle, FiClock } from 'react-icons/fi';
import { useSync, useNetwork, useAppDispatch } from '../store/hooks';
import { performFullSync, syncPendingChanges } from '../store/slices/syncSlice';

const SyncContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: #f5f5f5;
  border-radius: 8px;
  border: 1px solid #ddd;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: #e5e5e5;
  }

  &.offline {
    background: #fff3cd;
    border-color: #ffc107;
  }

  &.syncing {
    background: #d1ecf1;
    border-color: #17a2b8;
  }

  &.error {
    background: #f8d7da;
    border-color: #dc3545;
  }

  &.success {
    background: #d4edda;
    border-color: #28a745;
  }
`;

const IconWrapper = styled.div<{ $rotating?: boolean }>`
  display: flex;
  align-items: center;

  ${props => props.$rotating && `
    animation: rotate 1s linear infinite;
  `}

  @keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
  }
`;

const StatusText = styled.span`
  font-weight: 500;
`;

const PendingCount = styled.span`
  background: #007bff;
  color: white;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 0.75rem;
  font-weight: 600;
  min-width: 18px;
  text-align: center;
`;

const SyncStatusIndicator: React.FC = () => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const { isOnline } = useNetwork();
  const {
    isSyncing,
    syncStatus,
    pendingSyncCount,
    syncErrors,
    lastSyncTimestamp
  } = useSync();

  const handleClick = () => {
    if (isOnline && !isSyncing) {
      if (pendingSyncCount > 0) {
        dispatch(syncPendingChanges() as any);
      } else {
        dispatch(performFullSync() as any);
      }
    }
  };

  const getStatusIcon = () => {
    if (!isOnline) {
      return <FiWifiOff color="orange" />;
    }

    if (isSyncing) {
      return <FiRefreshCw color="blue" />;
    }

    if (syncErrors.length > 0) {
      return <FiAlertCircle color="red" />;
    }

    if (syncStatus === 'success') {
      return <FiCheck color="green" />;
    }

    if (pendingSyncCount > 0) {
      return <FiClock color="orange" />;
    }

    return <FiWifi color="green" />;
  };

  const getStatusText = () => {
    if (!isOnline) {
      return t('sync.offline');
    }

    if (isSyncing) {
      return t('sync.syncing');
    }

    if (syncErrors.length > 0) {
      return t('sync.error', { count: syncErrors.length });
    }

    if (pendingSyncCount > 0) {
      return t('sync.pending', { count: pendingSyncCount });
    }

    if (lastSyncTimestamp) {
      const timeDiff = Date.now() - lastSyncTimestamp;
      const minutes = Math.floor(timeDiff / 60000);
      if (minutes < 1) {
        return t('sync.synchronized');
      } else if (minutes < 60) {
        return t('sync.syncedMinutesAgo', { minutes });
      } else {
        const hours = Math.floor(minutes / 60);
        return t('sync.syncedHoursAgo', { hours });
      }
    }

    return t('sync.online');
  };

  const getContainerClass = () => {
    if (!isOnline) return 'offline';
    if (isSyncing) return 'syncing';
    if (syncErrors.length > 0) return 'error';
    if (syncStatus === 'success') return 'success';
    return '';
  };

  return (
    <SyncContainer
      className={getContainerClass()}
      onClick={handleClick}
      title={isOnline && !isSyncing ? t('sync.clickToSync') : undefined}
    >
      <IconWrapper $rotating={isSyncing}>
        {getStatusIcon()}
      </IconWrapper>

      <StatusText>{getStatusText()}</StatusText>

      {pendingSyncCount > 0 && (
        <PendingCount>{pendingSyncCount}</PendingCount>
      )}
    </SyncContainer>
  );
};

export default SyncStatusIndicator;
