import React from 'react';
import { useTranslation } from 'react-i18next';
import SessionsList from '../components/SessionsList';
import styled from 'styled-components';

const Container = styled.div`
  padding: 1.5rem;
  max-width: 1000px;
  margin: 0 auto;
  color: ${({ theme }) => theme.text};
`;

const Header = styled.div`
  margin-bottom: 2rem;
  
  h1 {
    font-size: 2.5rem;
    color: ${({ theme }) => theme.primary};
    margin-bottom: 1rem;
  }
  
  p {
    font-size: 1.1rem;
    color: ${({ theme }) => theme.textSecondary};
    line-height: 1.6;
  }
`;

const SessionsPage: React.FC = () => {
  const { t } = useTranslation();
  return (
    <Container>
      <Header>
        <h1>{t('sessions.title')}</h1>
        <p>{t('sessions.description')}</p>
      </Header>
      <SessionsList />
    </Container>
  );
};

export default SessionsPage;
