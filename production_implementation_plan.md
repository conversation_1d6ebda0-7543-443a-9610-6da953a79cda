# Piknowkyo Production Implementation Plan

## Offline Strategy & Update Mechanism
1. **Service Worker Implementation** (`src/firebase.ts`)
   - Cache static assets (HTML, CSS, JS, images)
   - Implement network-first then cache-fallback strategy
   - Add versioning to cache names for easy updates

2. **24h Update Check**
   - Add background sync in `src/services/syncService.ts`
   - Check for updates when:
     - 24h since last check
     - Network becomes available after being offline
   - Verify script updates in `public/assets/scripts/` (fr, en, es)

3. **Update Process**
   - Compare local manifest version with server
   - Download updated scripts/assets in background
   - Notify user via `src/components/Toast.tsx` when updates available
   - Apply updates on next app restart

## Subscription & Monetization Model
1. **Free Tier (Always Available)**
   - Meditation and story sessions
   - Music
   - Basic TTS voices on-device
   - Session statistics
   - Progress journal
   - Basic audio features (no binaural, no custom audio)
   - Anonymous leaderboard participation
   - Basic community features (blog, forums)
   - Basic notifications (no customization)
   - Premium features accessible via ads

2. **Premium Features (Require Payment)**
   - Games (`src/games/`)
   - All other sessions types
   - Advanced session analytics
   - Binaural beats and ambient sounds
   - Custom audio upload
   - No ads
   - Custom TTS voices (cloud-based)

3. **7-Day Free Trial**
   - Track trial status in `src/store/slices/authSlice.ts`
   - Show countdown in `src/components/BottomBar.tsx`

4. **Ad-Supported Unlocks**
   - Implement `src/services/adService.ts`
   - Use Google AdMob for rewarded ads
   - Unlock premium features for 1 hour after ad view
   - Track unlock status in Redux

## Firebase Optimization
1. **Minimize Reads/Writes**
   - Batch operations where possible
   - Use local caching with `src/services/useNetworkStatus.ts`
   - Only sync essential data

2. **Data Structure Optimization**
   - Flatten nested collections
   - Use composite indexes
   - Implement pagination for large datasets

## Production Readiness
1. **Profile Page** (`src/pages/ProfilePage.tsx`)
   - Replace mock data with real user data from Firebase
   - Add edit functionality
   - Connect to subscription status
   - Implement delete account functionality
   - Add paywall for premium features
   - Add payment processing via Stripe
   - Add subscription management via Stripe
   - Add billing history and management
   - Add support for multiple payment methods
   - Add support for different currencies
   - Add support for different languages (fr,en,es)

2. **Leaderboard Page** (`src/pages/LeaderboardPage.tsx`)
   - Implement real-time updates
   - Add filtering/sorting
   - Connect to game scores and sessions

3. **Other Pages**
   - Remove all placeholder content
   - Add loading states
   - Implement error boundaries

## Journal Synchronization
1. **Local Storage**
   - Store entries in IndexedDB via `src/services/syncService.ts`
   - Use localForage for simplified API

2. **24h Sync**
   - Daily background sync at 2AM local time
   - Manual sync trigger in `src/components/SyncStatusIndicator.tsx` (once per day)
   - Conflict resolution (last write wins)

3. **Multi-Device Access**
   - Store journal in user's Firestore collection
   - Timestamp all entries
   - Limit access to two devices per account

## PWA Installation Flow
1. **Install Prompt**
   - Detect first visit in `src/components/SplashScreen.tsx`
   - Show install prompt if:
     - User is logged in
     - App not installed
   - Track installation status in localStorage

2. **PWA Features**
   - Add manifest properties in `public/manifest.json`
   - Implement offline capabilities
   - Add splash screens

## Implementation Roadmap
1. **Phase 1: Offline Foundation**
   - Service worker setup
   - Asset caching
   - Update check mechanism

2. **Phase 2: Monetization**
   - Subscription system
   - Ad integration
   - Trial management

3. **Phase 3: Production Polish**
   - Page readiness
   - Journal sync
   - PWA installation flow
   - Final testing

4. **Phase 4: Deployment**
   - Firebase hosting setup
   - Performance optimization
   - App store submission (google play, app store)
   - Web version launch
   - Monitor and optimize
   - Gather user feedback
   - Iterate and improve
   