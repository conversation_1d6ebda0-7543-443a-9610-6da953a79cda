import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { AudioAsset, AudioManifest } from '../../models';

export interface AudioAssetsState {
  assets: AudioAsset[];
  manifest: AudioManifest | null;
  isLoading: boolean;
  error: string | null;
  lastSyncTimestamp: number | null;
  pendingChanges: {
    created: AudioAsset[];
    updated: AudioAsset[];
    deleted: string[];
  };
}

const initialState: AudioAssetsState = {
  assets: [],
  manifest: null,
  isLoading: false,
  error: null,
  lastSyncTimestamp: null,
  pendingChanges: {
    created: [],
    updated: [],
    deleted: [],
  },
};

export const fetchAudioAssets = createAsyncThunk(
  'audioAssets/fetchAssets',
  async (_, { rejectWithValue }) => {
    try {
      // Récupérer le manifeste local
      const response = await fetch('/assets/manifests/audio-assets.json');
      if (!response.ok) {
        // Si le fichier n'existe pas, retourner un manifeste vide
        return { assets: [], manifest: { musics: [], ambiants: [] } };
      }
      const manifest: AudioManifest = await response.json();
      
      // Combiner musiques et ambiants
      const allAssets = [...manifest.musics, ...manifest.ambiants];

      return { assets: allAssets, manifest };
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

const audioAssetsSlice = createSlice({
  name: 'audioAssets',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    updateLastSyncTimestamp: (state, action: PayloadAction<number>) => {
      state.lastSyncTimestamp = action.payload;
    },
    clearPendingChanges: (state) => {
      state.pendingChanges = {
        created: [],
        updated: [],
        deleted: [],
      };
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchAudioAssets.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchAudioAssets.fulfilled, (state, action) => {
        state.isLoading = false;
        state.assets = action.payload.assets;
        state.manifest = action.payload.manifest;
        state.lastSyncTimestamp = Date.now();
      })
      .addCase(fetchAudioAssets.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
  },
});

export const {
  clearError,
  updateLastSyncTimestamp,
  clearPendingChanges,
} = audioAssetsSlice.actions;

export default audioAssetsSlice.reducer;
