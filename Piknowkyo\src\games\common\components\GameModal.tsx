// src/games/common/components/GameModal.tsx

import React from 'react';
import styled from 'styled-components';
import ReusableModal from '../../../components/ReusableModal';
import { FiPlay, FiCornerDownLeft, FiRefreshCcw, FiMenu } from 'react-icons/fi';
import { useTranslation } from 'react-i18next';

const ModalActions = styled.div`
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-top: 1.5rem;

  button {
    background: ${({ theme }) => theme.primary};
    color: ${({ theme }) => theme.textLight};
    border: none;
    border-radius: 8px;
    padding: 0.8rem 1.5rem;
    font-size: 1rem;
    cursor: pointer;
    transition: background-color 0.2s, transform 0.1s;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;

    &:hover { opacity: 0.9; }
    &:active { transform: scale(0.98); }
    &:disabled { opacity: 0.6; cursor: not-allowed; }

    &.secondary {
        background: ${({ theme }) => theme.surfaceAlt};
        color: ${({ theme }) => theme.text};
        border: 1px solid ${({ theme }) => theme.border};
    }
    &.danger {
        background: ${({ theme }) => theme.errorColor};
    }
  }
`;

interface GameModalProps {
  isOpen: boolean;
  title: string;
  children: React.ReactNode;
  
  showStartButton?: boolean;
  showResumeButton?: boolean;
  showReturnButton?: boolean;
  showRestartButton?: boolean;

  onStart?: () => void;
  onResume?: () => void;
  onReturn?: () => void;
  onRestart?: () => void;

  isLoading?: boolean;
}

const GameModal: React.FC<GameModalProps> = ({
  isOpen, title, children,
  showStartButton, showResumeButton, showReturnButton, showRestartButton,
  onStart, onResume, onReturn, onRestart,
  isLoading
}) => {
  const { t } = useTranslation();

  return (
    <ReusableModal
      isOpen={isOpen}
      onClose={() => { if (showReturnButton && onReturn && !isLoading) onReturn(); }}
      title={title}
      titleIcon={<FiMenu />}
      isLoading={isLoading}
      footerContent={
        <ModalActions>
          {showReturnButton && (
            <button onClick={onReturn} className="secondary" disabled={isLoading}>
              <FiCornerDownLeft /> {t('game.modal.return', 'Retour')}
            </button>
          )}
          {showRestartButton && (
            <button onClick={onRestart} className="secondary" disabled={isLoading}>
              <FiRefreshCcw /> {t('game.modal.restart', 'Redémarrer')}
            </button>
          )}
          {showResumeButton && (
            <button onClick={onResume} disabled={isLoading}>
              <FiPlay /> {t('game.modal.resume', 'Reprendre')}
            </button>
          )}
          {showStartButton && (
            <button onClick={onStart} disabled={isLoading}>
              <FiPlay /> {t('game.modal.start', 'Commencer')}
            </button>
          )}
        </ModalActions>
      }
    >
      {children}
    </ReusableModal>
  );
};

export default GameModal;