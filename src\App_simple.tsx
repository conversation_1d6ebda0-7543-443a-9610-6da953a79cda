// src/App.tsx - Simplified version for production build
import React, { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { useAppDispatch } from './store/hooks';
import { fetchSessions } from './store/slices/sessionsSlice';
import { fetchJournalEntries } from './store/slices/journalSlice';
import { fetchAudioAssets } from './store/slices/audioAssetsSlice';
import { useAuth } from './hooks/useAuth';
import { usePushNotifications } from './services/usePushNotifications';

// Pages
import HomePage from './pages/HomePage';
import SessionsPage from './pages/SessionsPage';
import SessionDetailPage from './pages/SessionDetailPage';
import JournalPage from './pages/JournalPage';
import StatsPage from './pages/StatsPage';
import ProfilePage from './pages/ProfilePage';
import LeaderboardPage from './pages/LeaderboardPage';
import MonetizationPage from './pages/MonetizationPage';
import GamesPage from './pages/GamesPage';
import BlogPage from './pages/BlogPage';
import SettingsPage from './pages/SettingsPage';

// Components
import SplashScreen from './components/SplashScreen';
import AuthPage from './components/AuthPage';
import SyncStatusIndicator from './components/SyncStatusIndicator';
import NetworkStatusNotifier from './components/NetworkStatusNotifier';
import BottomBar from './components/BottomBar';

const App: React.FC = () => {
  const [showSplash, setShowSplash] = useState(true);
  const dispatch = useAppDispatch();
  const { user, loading: authLoading } = useAuth();

  usePushNotifications();

  // Hide splash screen after delay
  useEffect(() => {
    const timer = setTimeout(() => setShowSplash(false), 1200);
    return () => clearTimeout(timer);
  }, []);

  // Load basic data when user is authenticated
  useEffect(() => {
    if (!authLoading && user) {
      dispatch(fetchSessions());
      dispatch(fetchAudioAssets());
      dispatch(fetchJournalEntries());
    }
  }, [dispatch, user, authLoading]);

  // Show splash screen while loading
  if (showSplash || authLoading) {
    return <SplashScreen />;
  }

  // Show auth page if not authenticated
  if (!user) {
    return <AuthPage />;
  }

  // Main app
  return (
    <Router>
      <div className="app">
        <Routes>
          <Route path="/" element={<HomePage />} />
          <Route path="/sessions" element={<SessionsPage />} />
          <Route path="/sessions/:sessionId" element={<SessionDetailPage />} />
          <Route path="/journal" element={<JournalPage />} />
          <Route path="/stats" element={<StatsPage />} />
          <Route path="/profile" element={<ProfilePage />} />
          <Route path="/leaderboard" element={<LeaderboardPage />} />
          <Route path="/monetization" element={<MonetizationPage />} />
          <Route path="/games" element={<GamesPage />} />
          <Route path="/blog" element={<BlogPage />} />
          <Route path="/settings" element={<SettingsPage />} />
          <Route path="*" element={<HomePage />} />
        </Routes>

        <SyncStatusIndicator />
        <NetworkStatusNotifier />
        <BottomBar />
      </div>
    </Router>
  );
};

export default App;
