import { useEffect, useState } from 'react';
import { useAppDispatch } from '../store/hooks';
import { applyAdReward } from '../store/slices/subscriptionSlice';
import { adService, AdReward, AdShowResult } from '../services/adService';

export const useAds = () => {
  const dispatch = useAppDispatch();
  const [isAdReady, setIsAdReady] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Initialize ad service and set up reward callback
  useEffect(() => {
    const initializeAds = async () => {
      try {
        const initialized = await adService.initialize();
        if (initialized) {
          // Set up reward callback
          adService.onAdRewarded((reward: AdReward) => {
            dispatch(applyAdReward(reward));
          });
          
          // Check if ad is ready
          const ready = await adService.isRewardedAdReady();
          setIsAdReady(ready);
        }
      } catch (error) {
        console.error('Failed to initialize ads:', error);
        setError(error instanceof Error ? error.message : 'Ad initialization failed');
      }
    };

    initializeAds();
  }, [dispatch]);

  // Periodically check ad availability
  useEffect(() => {
    const checkAdAvailability = async () => {
      try {
        const ready = await adService.isRewardedAdReady();
        setIsAdReady(ready);
      } catch (error) {
        console.error('Failed to check ad availability:', error);
      }
    };

    const interval = setInterval(checkAdAvailability, 30000); // Check every 30 seconds
    return () => clearInterval(interval);
  }, []);

  const loadAd = async (): Promise<boolean> => {
    setIsLoading(true);
    setError(null);

    try {
      const result = await adService.loadRewardedAd();
      
      if (result.success) {
        setIsAdReady(true);
        return true;
      } else {
        setError(result.error || 'Failed to load ad');
        return false;
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      setError(errorMessage);
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const showAd = async (): Promise<AdShowResult> => {
    setIsLoading(true);
    setError(null);

    try {
      const result = await adService.showRewardedAd();
      
      if (result.success) {
        setIsAdReady(false); // Ad consumed, need to load new one
        
        if (result.rewarded && result.reward) {
          // Reward will be automatically applied via the callback
          console.log('Ad reward received:', result.reward);
        }
      } else {
        setError(result.error || 'Failed to show ad');
      }
      
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      setError(errorMessage);
      
      return {
        success: false,
        rewarded: false,
        error: errorMessage
      };
    } finally {
      setIsLoading(false);
    }
  };

  const watchAdForPremium = async (): Promise<boolean> => {
    try {
      // Load ad if not ready
      if (!isAdReady) {
        const loaded = await loadAd();
        if (!loaded) {
          return false;
        }
      }

      // Show ad
      const result = await showAd();
      
      if (result.success && result.rewarded) {
        // Premium features unlocked via Redux
        return true;
      }
      
      return false;
    } catch (error) {
      console.error('Failed to watch ad for premium:', error);
      return false;
    }
  };

  const getAdStatus = () => {
    return adService.getAdStatus();
  };

  const clearError = () => {
    setError(null);
  };

  // Auto-load ad when it becomes unavailable
  useEffect(() => {
    if (!isAdReady && !isLoading && !error) {
      loadAd();
    }
  }, [isAdReady, isLoading, error]);

  return {
    // State
    isAdReady,
    isLoading,
    error,
    
    // Actions
    loadAd,
    showAd,
    watchAdForPremium,
    clearError,
    
    // Helpers
    getAdStatus,
    
    // Quick access
    canWatchAd: isAdReady && !isLoading,
  };
};

export default useAds;
