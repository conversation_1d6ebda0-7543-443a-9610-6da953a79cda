import React from 'react';
import styled, { keyframes } from 'styled-components';
import { useTranslation } from 'react-i18next';

const fadeIn = keyframes`
  from { opacity: 0; }
  to { opacity: 1; }
`;

const Splash = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background: ${({ theme }) => theme.background};
  animation: ${fadeIn} 0.7s;
`;

const Logo = styled.img`
  width: 200px;
  margin-bottom: 2rem;
`;

const Title = styled.h1`
  color: ${({ theme }) => theme.primary};
  font-size: 2rem;
`;

const SplashScreen: React.FC = () => {
  const { t } = useTranslation();

  return (
    <Splash>
      <Logo src="/logo192.png" alt="Piknowkyo logo" />
      <Title>{t('app.name')}</Title>
    </Splash>
  );
};

export default SplashScreen;
