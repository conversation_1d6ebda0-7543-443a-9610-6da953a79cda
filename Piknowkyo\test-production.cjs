#!/usr/bin/env node

/**
 * PiKnowKyo Production Test Suite
 * Tests critical functionality on the production deployment
 */

const https = require('https');
const { URL } = require('url');

const PRODUCTION_URL = 'https://piknowkyo-777.web.app';
const TIMEOUT = 10000; // 10 seconds

class ProductionTester {
  constructor() {
    this.tests = [];
    this.results = [];
  }

  addTest(name, testFn) {
    this.tests.push({ name, testFn });
  }

  async runTests() {
    console.log('🧪 Starting PiKnowKyo Production Tests...\n');
    
    for (const test of this.tests) {
      try {
        console.log(`⏳ Running: ${test.name}`);
        const startTime = Date.now();
        await test.testFn();
        const duration = Date.now() - startTime;
        console.log(`✅ PASS: ${test.name} (${duration}ms)\n`);
        this.results.push({ name: test.name, status: 'PASS', duration });
      } catch (error) {
        console.log(`❌ FAIL: ${test.name}`);
        console.log(`   Error: ${error.message}\n`);
        this.results.push({ name: test.name, status: 'FAIL', error: error.message });
      }
    }

    this.printSummary();
  }

  printSummary() {
    console.log('📊 Test Summary:');
    console.log('================');
    
    const passed = this.results.filter(r => r.status === 'PASS').length;
    const failed = this.results.filter(r => r.status === 'FAIL').length;
    
    console.log(`Total Tests: ${this.results.length}`);
    console.log(`Passed: ${passed}`);
    console.log(`Failed: ${failed}`);
    
    if (failed === 0) {
      console.log('\n🎉 All tests passed! Production deployment is healthy.');
    } else {
      console.log('\n⚠️  Some tests failed. Please check the deployment.');
    }
  }

  async httpRequest(url, options = {}) {
    return new Promise((resolve, reject) => {
      const urlObj = new URL(url);
      const requestOptions = {
        hostname: urlObj.hostname,
        port: urlObj.port || 443,
        path: urlObj.pathname + urlObj.search,
        method: options.method || 'GET',
        timeout: TIMEOUT,
        ...options
      };

      const req = https.request(requestOptions, (res) => {
        let data = '';
        res.on('data', chunk => data += chunk);
        res.on('end', () => {
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            body: data
          });
        });
      });

      req.on('error', reject);
      req.on('timeout', () => {
        req.destroy();
        reject(new Error('Request timeout'));
      });

      req.end();
    });
  }
}

// Initialize tester
const tester = new ProductionTester();

// Test 1: Basic connectivity
tester.addTest('Basic Connectivity', async () => {
  const response = await tester.httpRequest(PRODUCTION_URL);
  if (response.statusCode !== 200) {
    throw new Error(`Expected status 200, got ${response.statusCode}`);
  }
});

// Test 2: HTML content validation
tester.addTest('HTML Content Validation', async () => {
  const response = await tester.httpRequest(PRODUCTION_URL);
  if (!response.body.includes('<title>')) {
    throw new Error('HTML title tag not found');
  }
  if (!response.body.includes('PiKnowKyo') && !response.body.includes('piknowkyo')) {
    throw new Error('App name not found in HTML');
  }
});

// Test 3: Static assets
tester.addTest('Static Assets Loading', async () => {
  const response = await tester.httpRequest(PRODUCTION_URL);
  const cssMatch = response.body.match(/href="([^"]*\.css)"/);
  const jsMatch = response.body.match(/src="([^"]*\.js)"/);
  
  if (!cssMatch) {
    throw new Error('CSS file reference not found');
  }
  if (!jsMatch) {
    throw new Error('JS file reference not found');
  }

  // Test CSS file
  const cssUrl = new URL(cssMatch[1], PRODUCTION_URL).href;
  const cssResponse = await tester.httpRequest(cssUrl);
  if (cssResponse.statusCode !== 200) {
    throw new Error(`CSS file not accessible: ${cssResponse.statusCode}`);
  }

  // Test JS file
  const jsUrl = new URL(jsMatch[1], PRODUCTION_URL).href;
  const jsResponse = await tester.httpRequest(jsUrl);
  if (jsResponse.statusCode !== 200) {
    throw new Error(`JS file not accessible: ${jsResponse.statusCode}`);
  }
});

// Test 4: Manifest files
tester.addTest('Session Manifests', async () => {
  const manifestUrls = [
    `${PRODUCTION_URL}/assets/manifests/manifest_fr.json`,
    `${PRODUCTION_URL}/assets/manifests/manifest_en.json`,
    `${PRODUCTION_URL}/assets/manifests/manifest_es.json`
  ];

  for (const url of manifestUrls) {
    const response = await tester.httpRequest(url);
    if (response.statusCode !== 200) {
      throw new Error(`Manifest not accessible: ${url}`);
    }
    
    try {
      const manifest = JSON.parse(response.body);
      if (!Array.isArray(manifest.sessions)) {
        throw new Error(`Invalid manifest format: ${url}`);
      }
    } catch (e) {
      throw new Error(`Invalid JSON in manifest: ${url}`);
    }
  }
});

// Test 5: Service Worker
tester.addTest('Service Worker', async () => {
  const swResponse = await tester.httpRequest(`${PRODUCTION_URL}/sw.js`);
  if (swResponse.statusCode !== 200) {
    throw new Error('Service Worker not accessible');
  }
  if (!swResponse.body.includes('workbox') && !swResponse.body.includes('cache')) {
    throw new Error('Service Worker content seems invalid');
  }
});

// Test 6: PWA Manifest
tester.addTest('PWA Manifest', async () => {
  const manifestResponse = await tester.httpRequest(`${PRODUCTION_URL}/manifest.json`);
  if (manifestResponse.statusCode !== 200) {
    throw new Error('PWA manifest not accessible');
  }
  
  try {
    const manifest = JSON.parse(manifestResponse.body);
    if (!manifest.name || !manifest.start_url) {
      throw new Error('Invalid PWA manifest structure');
    }
  } catch (e) {
    throw new Error('Invalid JSON in PWA manifest');
  }
});

// Test 7: Response time
tester.addTest('Response Time Performance', async () => {
  const startTime = Date.now();
  await tester.httpRequest(PRODUCTION_URL);
  const responseTime = Date.now() - startTime;
  
  if (responseTime > 5000) {
    throw new Error(`Response time too slow: ${responseTime}ms`);
  }
});

// Run all tests
tester.runTests().catch(console.error);
