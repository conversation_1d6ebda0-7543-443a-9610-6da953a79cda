#!/usr/bin/env node

/**
 * Production Readiness Test Suite for Piknowkyo
 * Tests all critical functionality before deployment
 */

const fs = require('fs');
const path = require('path');

class ProductionTester {
  constructor() {
    this.results = {
      passed: 0,
      failed: 0,
      warnings: 0,
      tests: []
    };
  }

  log(message, type = 'info') {
    const timestamp = new Date().toISOString();
    const prefix = {
      'info': '📋',
      'pass': '✅',
      'fail': '❌',
      'warn': '⚠️'
    }[type];
    
    console.log(`${prefix} [${timestamp}] ${message}`);
  }

  test(name, testFn) {
    try {
      const result = testFn();
      if (result === true) {
        this.results.passed++;
        this.results.tests.push({ name, status: 'passed' });
        this.log(`${name}: PASSED`, 'pass');
      } else if (result === 'warning') {
        this.results.warnings++;
        this.results.tests.push({ name, status: 'warning' });
        this.log(`${name}: WARNING`, 'warn');
      } else {
        this.results.failed++;
        this.results.tests.push({ name, status: 'failed', error: result });
        this.log(`${name}: FAILED - ${result}`, 'fail');
      }
    } catch (error) {
      this.results.failed++;
      this.results.tests.push({ name, status: 'failed', error: error.message });
      this.log(`${name}: FAILED - ${error.message}`, 'fail');
    }
  }

  fileExists(filePath) {
    return fs.existsSync(filePath);
  }

  fileContains(filePath, content) {
    if (!this.fileExists(filePath)) return false;
    const fileContent = fs.readFileSync(filePath, 'utf8');
    return fileContent.includes(content);
  }

  jsonValid(filePath) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      JSON.parse(content);
      return true;
    } catch {
      return false;
    }
  }

  runTests() {
    this.log('Starting Production Readiness Tests for Piknowkyo', 'info');
    this.log('='.repeat(60), 'info');

    // Test 1: Essential Files
    this.test('Firebase Configuration Exists', () => {
      return this.fileExists('firebase.json') || 'firebase.json not found';
    });

    this.test('Firestore Rules Exist', () => {
      return this.fileExists('firestore.rules') || 'firestore.rules not found';
    });

    this.test('Storage Rules Exist', () => {
      return this.fileExists('storage.rules') || 'storage.rules not found';
    });

    this.test('Package.json Valid', () => {
      return this.jsonValid('package.json') || 'package.json is invalid';
    });

    this.test('PWA Manifest Valid', () => {
      return this.jsonValid('public/manifest.json') || 'manifest.json is invalid';
    });

    // Test 2: Service Worker
    this.test('Service Worker Exists', () => {
      return this.fileExists('public/sw.js') || 'Service worker not found';
    });

    this.test('Service Worker Registration', () => {
      return this.fileContains('src/index.tsx', 'serviceWorkerManager') || 
             'Service worker not registered in index.tsx';
    });

    // Test 3: Core Components
    this.test('Update Service Exists', () => {
      return this.fileExists('src/services/updateService.ts') || 'Update service not found';
    });

    this.test('Ad Service Exists', () => {
      return this.fileExists('src/services/adService.ts') || 'Ad service not found';
    });

    this.test('Stripe Service Exists', () => {
      return this.fileExists('src/services/stripeService.ts') || 'Stripe service not found';
    });

    this.test('Subscription Slice Exists', () => {
      return this.fileExists('src/store/slices/subscriptionSlice.ts') || 'Subscription slice not found';
    });

    // Test 4: Premium Components
    this.test('Premium Gate Component', () => {
      return this.fileExists('src/components/PremiumGate.tsx') || 'PremiumGate component not found';
    });

    this.test('Subscription Manager Component', () => {
      return this.fileExists('src/components/SubscriptionManager.tsx') || 'SubscriptionManager component not found';
    });

    this.test('Payment History Component', () => {
      return this.fileExists('src/components/PaymentHistory.tsx') || 'PaymentHistory component not found';
    });

    this.test('Ad Reward Modal Component', () => {
      return this.fileExists('src/components/AdRewardModal.tsx') || 'AdRewardModal component not found';
    });

    // Test 5: Hooks
    this.test('Subscription Hook', () => {
      return this.fileExists('src/hooks/useSubscription.ts') || 'useSubscription hook not found';
    });

    this.test('Ads Hook', () => {
      return this.fileExists('src/hooks/useAds.ts') || 'useAds hook not found';
    });

    // Test 6: App Integration
    this.test('Update Notification in App', () => {
      return this.fileContains('src/App.tsx', 'UpdateNotification') || 
             'UpdateNotification not integrated in App.tsx';
    });

    this.test('Subscription Integration in ProfilePage', () => {
      return this.fileContains('src/pages/ProfilePage.tsx', 'SubscriptionManager') || 
             'SubscriptionManager not integrated in ProfilePage';
    });

    // Test 7: Redux Store
    this.test('Subscription Slice in Store', () => {
      return this.fileContains('src/store/index.ts', 'subscriptionSlice') || 
             'subscriptionSlice not added to store';
    });

    this.test('Store Persistence Configuration', () => {
      return this.fileContains('src/store/index.ts', 'subscription') && 
             this.fileContains('src/store/index.ts', 'whitelist') || 
             'Subscription not in persistence whitelist';
    });

    // Test 8: Environment Configuration
    this.test('Version Tracking File', () => {
      return this.fileExists('public/version.json') || 'version.json not found';
    });

    this.test('Version File Valid JSON', () => {
      return this.jsonValid('public/version.json') || 'version.json is invalid';
    });

    // Test 9: Security
    this.test('Firestore Rules Security', () => {
      const rules = fs.readFileSync('firestore.rules', 'utf8');
      return rules.includes('isAuthenticated()') && rules.includes('isOwner(') || 
             'Firestore rules lack proper authentication checks';
    });

    this.test('Storage Rules Security', () => {
      const rules = fs.readFileSync('storage.rules', 'utf8');
      return rules.includes('isAuthenticated()') && rules.includes('isOwner(') || 
             'Storage rules lack proper authentication checks';
    });

    // Test 10: Build Configuration
    this.test('Vite Config Exists', () => {
      return this.fileExists('vite.config.ts') || this.fileExists('vite.config.js') || 
             'Vite configuration not found';
    });

    this.test('TypeScript Config', () => {
      return this.fileExists('tsconfig.json') || 'TypeScript configuration not found';
    });

    // Test 11: Dependencies
    this.test('Firebase Dependencies', () => {
      const pkg = JSON.parse(fs.readFileSync('package.json', 'utf8'));
      return pkg.dependencies?.firebase || 'Firebase dependency not found';
    });

    this.test('Redux Dependencies', () => {
      const pkg = JSON.parse(fs.readFileSync('package.json', 'utf8'));
      return (pkg.dependencies?.['@reduxjs/toolkit'] && pkg.dependencies?.['react-redux']) || 
             'Redux dependencies not found';
    });

    this.test('Styled Components', () => {
      const pkg = JSON.parse(fs.readFileSync('package.json', 'utf8'));
      return pkg.dependencies?.['styled-components'] || 'styled-components dependency not found';
    });

    // Test 12: Assets
    this.test('PWA Icons', () => {
      return this.fileExists('public/logo192.png') && this.fileExists('public/logo512.png') || 
             'PWA icons not found';
    });

    this.test('Favicon', () => {
      return this.fileExists('public/favicon.ico') || 'Favicon not found';
    });

    // Test 13: Internationalization
    this.test('i18n Configuration', () => {
      return this.fileExists('src/i18n.ts') || 'i18n configuration not found';
    });

    this.test('Locale Files', () => {
      return this.fileExists('public/locales') || 'Locale files directory not found';
    });

    // Test 14: Performance
    this.test('Service Worker Caching Strategy', () => {
      const sw = fs.readFileSync('public/sw.js', 'utf8');
      return sw.includes('cacheFirst') && sw.includes('networkFirst') || 
             'Service worker lacks proper caching strategies';
    });

    // Test 15: Monetization
    this.test('Ad Service Integration', () => {
      return this.fileContains('src/services/adService.ts', 'AdMob') || 
             this.fileContains('src/services/adService.ts', 'rewarded') || 
             'Ad service lacks proper integration';
    });

    this.test('Stripe Integration', () => {
      return this.fileContains('src/services/stripeService.ts', 'checkout') && 
             this.fileContains('src/services/stripeService.ts', 'subscription') || 
             'Stripe service lacks proper integration';
    });

    this.printResults();
  }

  printResults() {
    this.log('='.repeat(60), 'info');
    this.log('Production Readiness Test Results', 'info');
    this.log('='.repeat(60), 'info');
    
    this.log(`Total Tests: ${this.results.passed + this.results.failed + this.results.warnings}`, 'info');
    this.log(`Passed: ${this.results.passed}`, 'pass');
    this.log(`Failed: ${this.results.failed}`, 'fail');
    this.log(`Warnings: ${this.results.warnings}`, 'warn');

    if (this.results.failed > 0) {
      this.log('\nFailed Tests:', 'fail');
      this.results.tests
        .filter(test => test.status === 'failed')
        .forEach(test => {
          this.log(`  - ${test.name}: ${test.error}`, 'fail');
        });
    }

    if (this.results.warnings > 0) {
      this.log('\nWarnings:', 'warn');
      this.results.tests
        .filter(test => test.status === 'warning')
        .forEach(test => {
          this.log(`  - ${test.name}`, 'warn');
        });
    }

    const score = Math.round((this.results.passed / (this.results.passed + this.results.failed)) * 100);
    this.log(`\nProduction Readiness Score: ${score}%`, score >= 90 ? 'pass' : score >= 70 ? 'warn' : 'fail');

    if (score >= 90) {
      this.log('🎉 Application is READY for production deployment!', 'pass');
    } else if (score >= 70) {
      this.log('⚠️  Application needs minor fixes before production deployment', 'warn');
    } else {
      this.log('❌ Application requires significant fixes before production deployment', 'fail');
    }

    return score >= 70;
  }
}

// Run tests
const tester = new ProductionTester();
const isReady = tester.runTests();

process.exit(isReady ? 0 : 1);
