// src/services/piperVoices.ts

// Interface pour une voix Piper, étendant TTSVoice ou spécifique si besoin
export interface PiperVoice {
  id: string;        // Identifiant unique (ex: fr_FR-upmc-medium)
  name: string;      // Souvent identique à l'id ou un nom plus descriptif
  lang: 'fr' | 'en' | 'es'; // Code langue
  label: string;     // Nom affiché à l'utilisateur (ex: "fr_FR-upmc (Femme, Medium)")
  modelUrl: string;  // URL du fichier .onnx
  configUrl: string; // URL du fichier .json de configuration
  quality?: string;   // 'low', 'medium', 'high', 'x_low', 'x_high'
  speaker?: string;   // Nom du locuteur principal si disponible
}

// Récupère dynamiquement la liste des voix Piper pour une langue donnée (fr, en, es)
// et donne aussi le lien du modèle .onnx et du fichier de config .json
export async function fetchPiperVoicesByLang(lang: 'fr' | 'en' | 'es'): Promise<PiperVoice[]> {
  // L'URL pointe vers le fichier VOICES.md dans le dépôt piper-voices de rhasspy
  // C'est une source plus directe et structurée pour les modèles.
  const baseUrl = 'https://huggingface.co/rhasspy/piper-voices/resolve/main/';
  const voicesUrl = `${baseUrl}${lang}/voices.json`; // Chaque langue a un voices.json

  try {
    const res = await fetch(voicesUrl);
    if (!res.ok) {
      console.error(`Impossible de récupérer la liste des voix Piper pour ${lang} depuis ${voicesUrl}. Statut: ${res.status}`);
      throw new Error(`Impossible de récupérer la liste des voix Piper pour ${lang}`);
    }
    
    const voicesData = await res.json();
    const processedVoices: PiperVoice[] = [];

    // voicesData est un objet où les clés sont les noms des modèles (ex: "fr_FR-upmc-medium")
    // et les valeurs sont des objets contenant des détails sur la voix.
    for (const modelKey in voicesData) {
      if (Object.prototype.hasOwnProperty.call(voicesData, modelKey)) {
        const voiceInfo = voicesData[modelKey];
        
        // Essayer de trouver les fichiers .onnx et .json pour ce modèle
        // Les noms de fichiers sont généralement modelKey.onnx et modelKey.onnx.json
        const modelFileName = `${modelKey}.onnx`;
        const configFileName = `${modelKey}.onnx.json`;

        // Vérifier si ces fichiers existent dans les 'files' listés pour cette voix
        // voiceInfo.files est un tableau de chaînes de noms de fichiers
        if (voiceInfo.files && voiceInfo.files.includes(modelFileName) && voiceInfo.files.includes(configFileName)) {
          
          // Construire les URLs complètes
          const modelUrl = `${baseUrl}${lang}/${modelKey}/${modelFileName}`;
          const configUrl = `${baseUrl}${lang}/${modelKey}/${configFileName}`;

          processedVoices.push({
            id: modelKey,
            name: voiceInfo.name || modelKey, // Utiliser le nom fourni s'il existe
            lang: lang,
            label: `${voiceInfo.name || modelKey} (${voiceInfo.quality || 'N/A'}, ${voiceInfo.num_speakers > 1 ? voiceInfo.num_speakers + ' speakers' : voiceInfo.speaker_id_map ? Object.values(voiceInfo.speaker_id_map)[0] : '1 speaker'})`,
            modelUrl: modelUrl,
            configUrl: configUrl,
            quality: voiceInfo.quality,
            speaker: voiceInfo.speaker_id_map ? Object.values(voiceInfo.speaker_id_map)[0] as string : undefined,
          });
        } else {
          console.warn(`Fichiers .onnx ou .json manquants pour le modèle Piper: ${modelKey} dans la langue ${lang}`);
        }
      }
    }
    return processedVoices;

  } catch (error) {
    console.error(`Erreur lors du fetch ou du parsing des voix Piper pour ${lang}:`, error);
    return []; // Retourner un tableau vide en cas d'erreur
  }
}

// Exemple d'utilisation (peut être placé dans un fichier de test ou un composant)
/*
async function testPiperVoices() {
  try {
    const frenchVoices = await fetchPiperVoicesByLang('fr');
    console.log('Voix Françaises Piper:', frenchVoices);

    const englishVoices = await fetchPiperVoicesByLang('en');
    console.log('Voix Anglaises Piper:', englishVoices);

    const spanishVoices = await fetchPiperVoicesByLang('es');
    console.log('Voix Espagnoles Piper:', spanishVoices);
  } catch (error) {
    console.error('Erreur lors du test des voix Piper:', error);
  }
}

// testPiperVoices();
*/