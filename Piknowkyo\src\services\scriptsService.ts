import { db } from '../firebase';
import { collection, addDoc, getDoc, updateDoc, doc, query, onSnapshot, QuerySnapshot, type DocumentData } from "firebase/firestore";
import type { Script } from '../models/script.model';

// Create a new script
export const addScript = async (script: Omit<Script, 'id'>): Promise<string> => {
  try {
    console.log('add script', db);
    const docRef = await addDoc(collection(db, "scripts"), script);
    return docRef.id;
  } catch (error) {
    console.error("Error adding script:", error);
    throw error;
  }
};

// Read a single script
export const getScript = async (scriptId: string): Promise<Script | null> => {
  try {
    const scriptDoc = await getDoc(doc(db, "scripts", scriptId));
    if (scriptDoc.exists()) {
      return { id: scriptDoc.id, ...scriptDoc.data() } as Script;
    } else {
      return null;
    }
  } catch (error) {
    console.error("Error getting script:", error);
    throw error;
  }
};

// Update a script
export const updateScript = async (scriptId: string, updates: Partial<Script>): Promise<void> => {
  try {
    await updateDoc(doc(db, "scripts", scriptId), updates);
  } catch (error) {
    console.error("Error updating script:", error);
    throw error;
  }
}

export const getScripts = (callback: (scripts: Script[]) => void): () => void => {
  const q = query(collection(db, "scripts"));

  const unsubscribe = onSnapshot(
    q,
    (querySnapshot: QuerySnapshot<DocumentData>) => {
      const scripts = querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
      }) as Script);
      callback(scripts);
    },
    (error) => {
      console.error("🔥 Firestore realtime error in getScripts:", error);
    }
  );

  return unsubscribe;
};
