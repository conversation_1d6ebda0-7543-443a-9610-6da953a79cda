// src/games/zen-tetris/GameComponent.tsx

import React, { useState, useEffect, useRef, useCallback, useContext } from 'react';
import { useTranslation } from 'react-i18next';
import { GameProps, SavedGameState } from '../common/models';
import { initializeTetrisGame, updateGame, move<PERSON><PERSON>ce, rotatePiece, TetrisGameState, GRID_WIDTH, GRID_HEIGHT } from './logic';
import { saveGameState, clearGameState, submitScoreToLeaderboard } from '../common/gameUtils';
import GameModal from '../common/components/GameModal';
import GameTimer from '../common/components/GameTimer';
import ScoreDisplay from '../common/components/ScoreDisplay';

import {
  GameContainer, TetrisBoard, Cell, SidePanel, NextPieceDisplay, InfoDisplay,
  PauseButton, MainControls, getPieceColor, GameArea, MobileControls, ControlButton
} from './styles';
import { FiClock, FiAward, FiHash, FiRotateCcw, FiArrowLeft, FiArrowRight, FiArrowDown, FiMenu, FiArrowUp, FiRotateCw } from 'react-icons/fi';
import { ThemeContext, DefaultTheme } from 'styled-components';
import { useOrientation } from './hooks/useOrientation';
import { hapticFeedback, isHapticSupported } from './utils/haptics';
import OrientationHint from './components/OrientationHint';

const GAME_ID = "zen-tetris";

const ZenTetrisGame: React.FC<GameProps> = ({
  userId, onGameEnd, onGameQuit, onPauseChange, initialGameState
}) => {
  const { t } = useTranslation();
  const theme = useContext(ThemeContext) as DefaultTheme;

  const [gameState, setGameState] = useState<TetrisGameState>(() => {
    if (initialGameState && initialGameState.specificGameState) {
      const loadedState = initialGameState.specificGameState;
      return {
        ...initializeTetrisGame(loadedState.level),
        ...loadedState,
        currentPiece: loadedState.currentPiece ? { ...loadedState.currentPiece } : null,
        nextPiece: loadedState.nextPiece ? { ...loadedState.nextPiece } : null,
        gameOver: false,
        isDropping: false,
      };
    }
    return initializeTetrisGame();
  });

  const [isPaused, setIsPaused] = useState(false);
  const [showRulesModal, setShowRulesModal] = useState(() => {
    const hasSeenRules = localStorage.getItem(`${GAME_ID}_seen_rules_${userId}`);
    // CORRECTION : Montrer les règles seulement si ce n'est pas une reprise et qu'elles n'ont pas été vues
    return !initialGameState && !hasSeenRules;
  });
  const [currentTimerSeconds, setCurrentTimerSeconds] = useState(initialGameState?.currentTimerSeconds || 0);

  const gameStateRef = useRef(gameState);
  gameStateRef.current = gameState;

  const timerIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const gameLoopIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // Touch controls state
  const touchStartRef = useRef<{ x: number; y: number; time: number } | null>(null);
  const boardRef = useRef<HTMLDivElement>(null);

  // Mobile enhancements
  const orientation = useOrientation();
  const hapticEnabled = isHapticSupported();

  // --- Fonctions de gestion du jeu ---

  const clearTimers = useCallback(() => {
    if (timerIntervalRef.current) clearInterval(timerIntervalRef.current);
    if (gameLoopIntervalRef.current) clearInterval(gameLoopIntervalRef.current);
    timerIntervalRef.current = null;
    gameLoopIntervalRef.current = null;
  }, []);

  const resumeGameLoop = useCallback(() => {
    clearTimers(); // S'assurer que les anciens timers sont nettoyés

    if (gameStateRef.current.gameOver || isPaused || showRulesModal) {
        // Ne pas démarrer les loops si le jeu est terminé, en pause, ou si les règles sont affichées
        return;
    }

    timerIntervalRef.current = setInterval(() => {
      setCurrentTimerSeconds(prev => prev + 1);
    }, 1000);

    const dropInterval = Math.max(100, 1000 - (gameStateRef.current.level - 1) * 9);

    gameLoopIntervalRef.current = setInterval(() => {
      setGameState(prev => {
        if (prev.gameOver || isPaused || showRulesModal) { // Double vérification ici
             clearTimers(); // Arrêter les timers si on entre dans cet état
             return prev;
        }
        const newState = updateGame(prev); // updateGame est movePiece(prev, 'down')
        if (newState.gameOver) {
          clearTimers();
          onGameEnd(newState.score, newState.level, currentTimerSeconds);
          clearGameState(GAME_ID, userId);
          submitScoreToLeaderboard({
            userId,
            gameId: GAME_ID,
            score: newState.score,
            levelReached: newState.level,
          });
        }
        return newState;
      });
    }, dropInterval);
  }, [isPaused, showRulesModal, onGameEnd, userId, currentTimerSeconds, clearTimers]); // Ajout de isPaused et showRulesModal


  const startGame = useCallback(() => {
    setShowRulesModal(false);
    localStorage.setItem(`${GAME_ID}_seen_rules_${userId}`, 'true');
    setIsPaused(false);
    onPauseChange(false);

    // Si c'est une reprise (initialGameState existe), l'état est déjà chargé.
    // Si ce n'est pas une reprise, ou si on veut explicitement recommencer via la modale de règles.
    if (!initialGameState || (initialGameState && showRulesModal)) {
      setGameState(initializeTetrisGame());
      setCurrentTimerSeconds(0);
    }
    // CORRECTION: resumeGameLoop sera appelé par le useEffect qui écoute showRulesModal et isPaused
  }, [initialGameState, onPauseChange, userId, showRulesModal]); // Ajout de showRulesModal ici

  const pauseGame = useCallback(() => {
    setIsPaused(true);
    onPauseChange(true);
    clearTimers(); // Arrêter les intervalles quand on met en pause

    saveGameState(GAME_ID, userId, {
      score: gameStateRef.current.score,
      level: gameStateRef.current.level,
      currentTimerSeconds: currentTimerSeconds,
      specificGameState: gameStateRef.current,
      lastPlayed: new Date(),
    });
  }, [currentTimerSeconds, onPauseChange, userId, clearTimers]);

  const resumeGame = useCallback(() => {
    setIsPaused(false);
    onPauseChange(false);
    // CORRECTION: resumeGameLoop sera appelé par le useEffect
  }, [onPauseChange]);

  const restartGame = useCallback(() => {
    clearGameState(GAME_ID, userId);
    setGameState(initializeTetrisGame());
    setCurrentTimerSeconds(0);
    setIsPaused(false);
    onPauseChange(false);
    setShowRulesModal(false); // Important pour que le bouton pause réapparaisse
    // CORRECTION: resumeGameLoop sera appelé par le useEffect
  }, [userId, onPauseChange]);

  const quitGame = useCallback(() => {
    clearTimers(); // S'assurer que tout est arrêté
    if (!gameStateRef.current.gameOver) {
      saveGameState(GAME_ID, userId, {
        score: gameStateRef.current.score,
        level: gameStateRef.current.level,
        currentTimerSeconds: currentTimerSeconds,
        specificGameState: gameStateRef.current,
        lastPlayed: new Date(),
      });
    } else {
      clearGameState(GAME_ID, userId);
      if (gameStateRef.current.score > 0) {
        submitScoreToLeaderboard({
          userId,
          gameId: GAME_ID,
          score: gameStateRef.current.score,
          levelReached: gameStateRef.current.level,
        });
      }
    }
    onGameQuit();
  }, [userId, currentTimerSeconds, onGameQuit, clearTimers]);

  // --- Touch Controls Functions ---
  const handleTouchStart = useCallback((e: React.TouchEvent) => {
    if (isPaused || gameState.gameOver || showRulesModal) return;

    const touch = e.touches[0];
    touchStartRef.current = {
      x: touch.clientX,
      y: touch.clientY,
      time: Date.now()
    };
  }, [isPaused, gameState.gameOver, showRulesModal]);

  const handleTouchEnd = useCallback((e: React.TouchEvent) => {
    if (isPaused || gameState.gameOver || showRulesModal || !touchStartRef.current) return;

    const touch = e.changedTouches[0];
    const deltaX = touch.clientX - touchStartRef.current.x;
    const deltaY = touch.clientY - touchStartRef.current.y;
    const deltaTime = Date.now() - touchStartRef.current.time;

    const minSwipeDistance = 30;
    const maxTapTime = 200;

    // Tap detection (quick touch)
    if (Math.abs(deltaX) < minSwipeDistance && Math.abs(deltaY) < minSwipeDistance && deltaTime < maxTapTime) {
      // Tap to rotate
      if (hapticEnabled) hapticFeedback.medium();
      setGameState(prev => rotatePiece(prev));
    } else {
      // Swipe detection
      if (Math.abs(deltaX) > Math.abs(deltaY)) {
        // Horizontal swipe
        if (deltaX > minSwipeDistance) {
          // Swipe right
          if (hapticEnabled) hapticFeedback.light();
          setGameState(prev => movePiece(prev, 'right'));
        } else if (deltaX < -minSwipeDistance) {
          // Swipe left
          if (hapticEnabled) hapticFeedback.light();
          setGameState(prev => movePiece(prev, 'left'));
        }
      } else {
        // Vertical swipe
        if (deltaY > minSwipeDistance) {
          // Swipe down
          if (hapticEnabled) hapticFeedback.light();
          setGameState(prev => movePiece(prev, 'down'));
        }
      }
    }

    touchStartRef.current = null;
  }, [isPaused, gameState.gameOver, showRulesModal]);

  const handleControlButtonPress = useCallback((action: 'left' | 'right' | 'down' | 'rotate') => {
    if (isPaused || gameState.gameOver || showRulesModal) return;

    // Add haptic feedback
    if (hapticEnabled) {
      if (action === 'rotate') {
        hapticFeedback.medium();
      } else {
        hapticFeedback.light();
      }
    }

    setGameState(prev => {
      switch (action) {
        case 'left':
          return movePiece(prev, 'left');
        case 'right':
          return movePiece(prev, 'right');
        case 'down':
          return movePiece(prev, 'down');
        case 'rotate':
          return rotatePiece(prev);
        default:
          return prev;
      }
    });
  }, [isPaused, gameState.gameOver, showRulesModal, hapticEnabled]);

  // --- Effets de bord ---

  // Nettoyage des intervalles au démontage
  useEffect(() => {
    return () => {
      clearTimers();
      onPauseChange(false); // S'assurer que le parent sait qu'on n'est plus en pause
    };
  }, [clearTimers, onPauseChange]);

  // CORRECTION MAJEURE: Gérer le démarrage/reprise de la boucle de jeu
  useEffect(() => {
    if (!gameState.gameOver && !isPaused && !showRulesModal) {
      resumeGameLoop();
    } else {
      clearTimers(); // Si en pause, game over ou règles, on s'assure que les timers sont arrêtés
    }
    // Le nettoyage de cet effet arrêtera les timers si l'une de ces conditions change pour "arrêter"
    return clearTimers;
  }, [gameState.gameOver, isPaused, showRulesModal, resumeGameLoop, clearTimers]);


  // Gestion des entrées clavier
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (isPaused || gameStateRef.current.gameOver || showRulesModal) return;

      // Empêcher le défilement de la page avec les flèches
      if (['ArrowLeft', 'ArrowRight', 'ArrowDown', 'ArrowUp', ' '].includes(event.key)) {
        event.preventDefault();
      }

      setGameState(prev => {
        let newState = prev;
        switch (event.key) {
          case 'ArrowLeft':
            newState = movePiece(prev, 'left');
            if (isHapticSupported()) hapticFeedback.light();
            break;
          case 'ArrowRight':
            newState = movePiece(prev, 'right');
            if (isHapticSupported()) hapticFeedback.light();
            break;
          case 'ArrowDown':
            newState = movePiece(prev, 'down');
            if (isHapticSupported()) hapticFeedback.light();
            break;
          case 'ArrowUp':
          case ' ': // Barre d'espace
            newState = rotatePiece(prev);
            if (isHapticSupported()) hapticFeedback.medium();
            break;
          case 'Escape':
            pauseGame();
            break;
          default:
            break;
        }
        return newState;
      });
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [isPaused, showRulesModal, pauseGame]);


  // Helper pour obtenir la grille avec la pièce courante pour le rendu
  const getRenderGrid = useCallback(() => {
    if (!gameState.currentPiece) return gameState.grid;

    const renderGrid = gameState.grid.map(row => [...row]);
    gameState.currentPiece.shape.forEach((row, y) => {
      row.forEach((cell, x) => {
        if (cell !== 0) {
          const gridY = gameState.currentPiece!.y + y;
          const gridX = gameState.currentPiece!.x + x;
          if (gridY >= 0 && gridY < GRID_HEIGHT && gridX >= 0 && gridX < GRID_WIDTH) {
            renderGrid[gridY][gridX] = gameState.currentPiece!.color;
          }
        }
      });
    });
    return renderGrid;
  }, [gameState]);

  const renderGrid = getRenderGrid();

  // CORRECTION: S'assurer que showRulesModal est false quand on clique sur Start
  const handleStartFromRules = () => {
    setShowRulesModal(false); // Ferme la modale
    startGame(); // Puis démarre le jeu (qui appellera resumeGameLoop via useEffect)
  };

  return (
    <GameContainer>
      <OrientationHint />

      {/* Bouton Pause - CORRECTION : il doit être visible si le jeu n'est pas fini ET que les règles ne sont pas affichées */}
      {!gameState.gameOver && !showRulesModal && (
        <PauseButton onClick={pauseGame} title={t('game.pauseButton', 'Mettre en pause')}>
          <FiMenu />
        </PauseButton>
      )}

      <GameArea>
        <SidePanel>
          <ScoreDisplay score={gameState.score} />
          <GameTimer timeInSeconds={currentTimerSeconds} />
          <InfoDisplay>
            <h4>{t('game.info', 'Infos')}</h4>
            <p><FiHash /> {t('game.level', 'Niveau')}: {gameState.level}</p>
            <p><FiAward /> {t('game.lines', 'Lignes')}: {gameState.linesCleared}</p>
          </InfoDisplay>
          {gameState.nextPiece && (
            <NextPieceDisplay>
              <h4>{t('game.nextPiece', 'Prochaine Pièce')}</h4>
              <div className="preview-grid">
                {Array(4).fill(0).map((_, rIdx) => (
                  Array(4).fill(0).map((_2, cIdx) => {
                    const isPieceCell = gameState.nextPiece &&
                      rIdx < gameState.nextPiece.shape.length &&
                      cIdx < gameState.nextPiece.shape[rIdx].length &&
                      gameState.nextPiece.shape[rIdx][cIdx] !== 0;
                    return (
                      <Cell
                        key={`next-${rIdx}-${cIdx}`}
                        $colorIndex={isPieceCell ? gameState.nextPiece!.color : 0}
                      />
                    );
                  })
                ))}
              </div>
            </NextPieceDisplay>
          )}
        </SidePanel>

        <TetrisBoard
          ref={boardRef}
          rows={GRID_HEIGHT}
          cols={GRID_WIDTH}
          onTouchStart={handleTouchStart}
          onTouchEnd={handleTouchEnd}
        >
          {renderGrid.map((row, rIdx) =>
            row.map((cell, cIdx) => (
              <Cell key={`${rIdx}-${cIdx}`} $colorIndex={cell || 0} />
            ))
          )}
        </TetrisBoard>
      </GameArea>

      {/* Desktop Controls */}
      <MainControls className="desktop-only">
        <button onClick={() => handleControlButtonPress('left')} title={t('game.moveLeft', 'Déplacer à gauche')}>
          <FiArrowLeft />
        </button>
        <button onClick={() => handleControlButtonPress('down')} title={t('game.softDrop', 'Chute douce')}>
          <FiArrowDown />
        </button>
        <button onClick={() => handleControlButtonPress('right')} title={t('game.moveRight', 'Déplacer à droite')}>
          <FiArrowRight />
        </button>
        <button onClick={() => handleControlButtonPress('rotate')} title={t('game.rotate', 'Rotation')}>
          <FiRotateCcw />
        </button>
      </MainControls>

      {/* Mobile Controls */}
      <MobileControls className="mobile-only">
        <div className="control-row">
          <ControlButton
            onClick={() => handleControlButtonPress('left')}
            title={t('game.moveLeft', 'Déplacer à gauche')}
            className="move-button"
          >
            <FiArrowLeft />
          </ControlButton>
          <ControlButton
            onClick={() => handleControlButtonPress('down')}
            title={t('game.softDrop', 'Chute douce')}
            className="drop-button"
          >
            <FiArrowDown />
          </ControlButton>
          <ControlButton
            onClick={() => handleControlButtonPress('right')}
            title={t('game.moveRight', 'Déplacer à droite')}
            className="move-button"
          >
            <FiArrowRight />
          </ControlButton>
        </div>
        <div className="control-row">
          <ControlButton
            onClick={() => handleControlButtonPress('rotate')}
            title={t('game.rotate', 'Rotation')}
            className="rotate-button"
          >
            <FiRotateCw />
          </ControlButton>
        </div>
      </MobileControls>

      <GameModal
        isOpen={showRulesModal || isPaused || gameState.gameOver}
        title={showRulesModal ? t('game.modal.rulesTitle', 'Règles du Jeu') :
               isPaused ? t('game.modal.pausedTitle', 'Jeu en Pause') :
               t('game.modal.gameOverTitle', 'Partie Terminée !')}
        // CORRECTION: Logique des boutons de la modale
        showStartButton={showRulesModal} // Toujours montrer "Start" si les règles sont affichées
        showResumeButton={isPaused && !showRulesModal && !gameState.gameOver}
        showReturnButton={true} // Toujours montrer "Retour"
        showRestartButton={isPaused || gameState.gameOver}
        onStart={handleStartFromRules} // Utiliser la nouvelle fonction pour fermer les règles ET démarrer
        onRestart={restartGame}
        onResume={resumeGame}
        onReturn={quitGame}
      >
        {showRulesModal && (
          <div>
            <p>{t('game.zenTetris.rules1', 'Empilez les blocs pour former des lignes complètes et marquez des points. La vitesse augmente avec les niveaux !')}</p>
            <p>{t('game.zenTetris.rules2', 'Contrôles :')}</p>
            <div style={{ marginBottom: '1rem' }}>
              <h4 style={{ margin: '0.5rem 0', color: '#B084CC' }}>{t('game.controls.keyboard')} :</h4>
              <ul style={{ margin: '0.5rem 0' }}>
                <li><FiArrowLeft /> : {t('game.zenTetris.ruleMoveLeft', 'Déplacer à gauche')}</li>
                <li><FiArrowRight /> : {t('game.zenTetris.ruleMoveRight', 'Déplacer à droite')}</li>
                <li><FiArrowDown /> : {t('game.zenTetris.ruleSoftDrop', 'Chute douce (soft drop)')}</li>
                <li><FiArrowUp /> (ou <FiRotateCcw />) : {t('game.zenTetris.ruleRotate', 'Rotation')}</li>
                <li>`Esc` : {t('game.zenTetris.rulePause', 'Pause')}</li>
              </ul>
            </div>
            <div>
              <h4 style={{ margin: '0.5rem 0', color: '#B084CC' }}>{t('games.zenTetris.controls', 'Contrôles tactiles')} :</h4>
              <ul style={{ margin: '0.5rem 0' }}>
                <li>👆 {t('games.zenTetris.touchTap', 'Tap rapide : Rotation')}</li>
                <li>⬅️ {t('games.zenTetris.touchLeft', 'Glisser gauche : Déplacer à gauche')}</li>
                <li>➡️ {t('games.zenTetris.touchRight', 'Glisser droite : Déplacer à droite')}</li>
                <li>⬇️ {t('games.zenTetris.touchDown', 'Glisser bas : Chute douce')}</li>
                <li>🔘 {t('games.zenTetris.touchButtons', 'Boutons de contrôle en bas')}</li>
              </ul>
            </div>
          </div>
        )}
        {isPaused && !showRulesModal && !gameState.gameOver && (
          <p>{t('game.modal.pausedMessage', 'Votre partie est en pause. Reprenez quand vous êtes prêt.')}</p>
        )}
        {gameState.gameOver && (
          <div>
            <p>{t('game.modal.gameOverMessage', 'Bien joué ! Votre score final est de {{score}} et vous avez atteint le niveau {{level}}.', { score: gameState.score, level: gameState.level })}</p>
          </div>
        )}
      </GameModal>
    </GameContainer>
  );
};

export default ZenTetrisGame;