// src/components/ReduxTestComponent.tsx
import React from 'react';
import styled from 'styled-components';
import { useAppSelector, useAppDispatch } from '../store/hooks';
import { useAuth } from '../hooks/useAuth';
import { useSessions, useUserProfile } from '../store/hooks';

const TestContainer = styled.div`
  padding: 1rem;
  margin: 1rem;
  border: 1px solid ${({ theme }) => theme.border};
  border-radius: 8px;
  background: ${({ theme }) => theme.surface};
`;

const TestSection = styled.div`
  margin-bottom: 1rem;
  padding: 0.5rem;
  border-left: 3px solid ${({ theme }) => theme.primary};
  background: ${({ theme }) => theme.surfaceAlt};
`;

const ReduxTestComponent: React.FC = () => {
  const dispatch = useAppDispatch();
  
  // Test des hooks Redux
  const { user } = useAuth();
  const { sessions, loading: sessionsLoading, error: sessionsError } = useSessions();
  const { profile: userProfile, loading: profileLoading, error: profileError } = useUserProfile();

  // Test des sélecteurs directs
  const authState = useAppSelector(state => state.auth);
  const sessionsState = useAppSelector(state => state.sessions);
  const userProfileState = useAppSelector(state => state.userProfile);

  return (
    <TestContainer>
      <h3>🧪 Redux Test Component</h3>
      
      <TestSection>
        <h4>Auth State</h4>
        <p>User: {user ? user.email : 'Not logged in'}</p>
        <p>Auth Loading: {authState.isLoading ? 'Yes' : 'No'}</p>
        <p>Auth Error: {authState.error || 'None'}</p>
      </TestSection>

      <TestSection>
        <h4>Sessions State</h4>
        <p>Sessions Count: {sessions.length}</p>
        <p>Sessions Loading: {sessionsLoading ? 'Yes' : 'No'}</p>
        <p>Sessions Error: {sessionsError || 'None'}</p>
        <p>Direct State Loading: {sessionsState.loading ? 'Yes' : 'No'}</p>
      </TestSection>

      <TestSection>
        <h4>User Profile State</h4>
        <p>Profile: {userProfile ? userProfile.email : 'No profile'}</p>
        <p>Profile Loading: {profileLoading ? 'Yes' : 'No'}</p>
        <p>Profile Error: {profileError || 'None'}</p>
        <p>Direct State Loading: {userProfileState.loading ? 'Yes' : 'No'}</p>
      </TestSection>

      <TestSection>
        <h4>Redux Store Status</h4>
        <p>✅ Redux hooks working</p>
        <p>✅ Selectors working</p>
        <p>✅ TypeScript types working</p>
      </TestSection>
    </TestContainer>
  );
};

export default ReduxTestComponent;
