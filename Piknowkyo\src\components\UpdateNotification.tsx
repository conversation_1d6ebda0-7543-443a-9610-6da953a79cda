import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { useTranslation } from 'react-i18next';
import { FiDownload, FiRefreshCw, FiX, FiInfo } from 'react-icons/fi';
import { updateService, UpdateCheckResult } from '../services/updateService';
import Button from './ui/Button';

const NotificationContainer = styled.div<{ $visible: boolean }>`
  position: fixed;
  top: ${({ $visible }) => $visible ? '20px' : '-100px'};
  left: 50%;
  transform: translateX(-50%);
  background: ${({ theme }) => theme.surface};
  color: ${({ theme }) => theme.text};
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  padding: 1rem 1.5rem;
  z-index: 1000;
  max-width: 400px;
  width: 90%;
  transition: all 0.3s ease-in-out;
  border: 1px solid ${({ theme }) => theme.border};

  @media (max-width: 768px) {
    top: ${({ $visible }) => $visible ? '10px' : '-100px'};
    max-width: 350px;
    padding: 0.875rem 1.25rem;
  }
`;

const Header = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.75rem;
`;

const Title = styled.h3`
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: ${({ theme }) => theme.primary};
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  color: ${({ theme }) => theme.textSecondary};
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;

  &:hover {
    background: ${({ theme }) => theme.border};
    color: ${({ theme }) => theme.text};
  }
`;

const Content = styled.div`
  margin-bottom: 1rem;
`;

const Description = styled.p`
  margin: 0 0 0.5rem 0;
  font-size: 0.875rem;
  line-height: 1.4;
  color: ${({ theme }) => theme.textSecondary};
`;

const VersionInfo = styled.div`
  font-size: 0.75rem;
  color: ${({ theme }) => theme.textSecondary};
  background: ${({ theme }) => theme.background};
  padding: 0.5rem;
  border-radius: 6px;
  margin: 0.5rem 0;
`;

const UpdateType = styled.span<{ $type: string }>`
  display: inline-block;
  padding: 0.125rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  margin-left: 0.5rem;
  
  ${({ $type, theme }) => {
    switch ($type) {
      case 'major':
        return `background: #ff4757; color: white;`;
      case 'minor':
        return `background: #ffa502; color: white;`;
      case 'patch':
        return `background: #2ed573; color: white;`;
      case 'scripts':
        return `background: ${theme.primary}; color: white;`;
      case 'assets':
        return `background: #5352ed; color: white;`;
      default:
        return `background: ${theme.border}; color: ${theme.text};`;
    }
  }}
`;

const Actions = styled.div`
  display: flex;
  gap: 0.75rem;
  align-items: center;

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 0.5rem;
  }
`;

const UpdateNotification: React.FC = () => {
  const { t } = useTranslation();
  const [updateInfo, setUpdateInfo] = useState<UpdateCheckResult | null>(null);
  const [isVisible, setIsVisible] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [isDismissed, setIsDismissed] = useState(false);

  useEffect(() => {
    // Listen for update notifications
    const handleUpdateAvailable = (result: UpdateCheckResult) => {
      if (!isDismissed) {
        setUpdateInfo(result);
        setIsVisible(true);
      }
    };

    updateService.onUpdateAvailable(handleUpdateAvailable);

    // Check for updates on mount
    updateService.forceUpdateCheck();

    return () => {
      // Note: updateService doesn't have removeListener, 
      // but in a real implementation you'd want to clean up
    };
  }, [isDismissed]);

  const handleUpdate = async () => {
    if (!updateInfo) return;

    setIsUpdating(true);
    
    try {
      const success = await updateService.applyUpdate(updateInfo);
      
      if (success) {
        if (updateInfo.requiresReload) {
          // App will reload automatically
          return;
        } else {
          // Update applied successfully without reload
          setIsVisible(false);
          setUpdateInfo(null);
        }
      } else {
        console.error('Update failed');
        // Show error message
      }
    } catch (error) {
      console.error('Update error:', error);
    } finally {
      setIsUpdating(false);
    }
  };

  const handleDismiss = () => {
    setIsVisible(false);
    setIsDismissed(true);
    
    // Auto-show again after 1 hour
    setTimeout(() => {
      setIsDismissed(false);
    }, 60 * 60 * 1000);
  };

  const handleLater = () => {
    setIsVisible(false);
    
    // Show again after 30 minutes
    setTimeout(() => {
      if (updateInfo && !isDismissed) {
        setIsVisible(true);
      }
    }, 30 * 60 * 1000);
  };

  const getUpdateTypeLabel = (type: string): string => {
    switch (type) {
      case 'major':
        return t('update.type.major', 'Major Update');
      case 'minor':
        return t('update.type.minor', 'Minor Update');
      case 'patch':
        return t('update.type.patch', 'Bug Fixes');
      case 'scripts':
        return t('update.type.scripts', 'Content Update');
      case 'assets':
        return t('update.type.assets', 'Asset Update');
      default:
        return t('update.type.unknown', 'Update');
    }
  };

  const getUpdateDescription = (type: string): string => {
    switch (type) {
      case 'major':
        return t('update.description.major', 'A major update with new features is available.');
      case 'minor':
        return t('update.description.minor', 'A new version with improvements is available.');
      case 'patch':
        return t('update.description.patch', 'Bug fixes and performance improvements are available.');
      case 'scripts':
        return t('update.description.scripts', 'New meditation sessions and content are available.');
      case 'assets':
        return t('update.description.assets', 'Updated audio and visual assets are available.');
      default:
        return t('update.description.default', 'An update is available for the app.');
    }
  };

  if (!updateInfo || !isVisible) {
    return null;
  }

  return (
    <NotificationContainer $visible={isVisible}>
      <Header>
        <Title>
          <FiDownload size={16} />
          {t('update.title', 'Update Available')}
          <UpdateType $type={updateInfo.updateType}>
            {getUpdateTypeLabel(updateInfo.updateType)}
          </UpdateType>
        </Title>
        <CloseButton onClick={handleDismiss} aria-label={t('common.close', 'Close')}>
          <FiX size={16} />
        </CloseButton>
      </Header>

      <Content>
        <Description>
          {getUpdateDescription(updateInfo.updateType)}
        </Description>

        <VersionInfo>
          <div>
            {t('update.currentVersion', 'Current')}: {updateInfo.currentVersion.version}
          </div>
          <div>
            {t('update.latestVersion', 'Latest')}: {updateInfo.latestVersion.version}
          </div>
        </VersionInfo>
      </Content>

      <Actions>
        <Button
          variant="primary"
          size="small"
          onClick={handleUpdate}
          disabled={isUpdating}
        >
          {isUpdating ? (
            <>
              <FiRefreshCw size={14} />
              {updateInfo.requiresReload 
                ? t('update.installing', 'Installing...')
                : t('update.downloading', 'Downloading...')
              }
            </>
          ) : (
            <>
              <FiDownload size={14} />
              {updateInfo.requiresReload 
                ? t('update.installRestart', 'Install & Restart')
                : t('update.download', 'Download')
              }
            </>
          )}
        </Button>

        <Button
          variant="secondary"
          size="small"
          onClick={handleLater}
          disabled={isUpdating}
        >
          {t('update.later', 'Later')}
        </Button>

        {updateInfo.updateType === 'assets' || updateInfo.updateType === 'scripts' ? (
          <Button
            variant="ghost"
            size="small"
            onClick={handleDismiss}
            disabled={isUpdating}
          >
            <FiInfo size={14} />
            {t('update.optional', 'Optional')}
          </Button>
        ) : null}
      </Actions>
    </NotificationContainer>
  );
};

export default UpdateNotification;
