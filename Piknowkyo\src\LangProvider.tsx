import { createContext, useContext, useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';

export type Language = 'fr' | 'en' | 'es';

const defaultLang: Language = 'fr';

interface LangContextType {
  lang: Language;
  setLang: (l: Language) => void;
}

export const LangContext = createContext<LangContextType>({
  lang: defaultLang,
  setLang: () => {},
});

export const useLang = () => useContext(LangContext);

export const LangProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { i18n } = useTranslation();
  const [lang, setLang] = useState<Language>(defaultLang);

  // Sync with i18n language changes
  useEffect(() => {
    const currentLang = i18n.language as Language;
    if (currentLang !== lang) {
      setLang(currentLang);
    }
  }, [i18n.language, lang]);

  // Update i18n when lang changes
  const handleSetLang = (newLang: Language) => {
    setLang(newLang);
    i18n.changeLanguage(newLang);
  };

  return (
    <LangContext.Provider value={{ lang, setLang: handleSetLang }}>
      {children}
    </LangContext.Provider>
  );
};
