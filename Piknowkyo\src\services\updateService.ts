// Update Service for Piknowkyo
// Handles app updates, asset versioning, and update notifications

import { serviceWorkerManager } from './serviceWorkerManager';

export interface AppVersion {
  version: string;
  buildDate: string;
  manifestVersion: string;
  scriptsVersion: string;
  assetsVersion: string;
}

export interface UpdateCheckResult {
  hasUpdate: boolean;
  currentVersion: AppVersion;
  latestVersion: AppVersion;
  updateType: 'major' | 'minor' | 'patch' | 'assets' | 'scripts';
  requiresReload: boolean;
}

export interface ManifestVersion {
  version: string;
  buildDate: string;
  scripts: {
    fr: string;
    en: string;
    es: string;
  };
  assets: {
    audio: string;
    images: string;
  };
}

class UpdateService {
  private currentVersion: AppVersion;
  private lastUpdateCheck: number = 0;
  private updateCheckInterval: number = 24 * 60 * 60 * 1000; // 24 hours
  private updateCallbacks: ((result: UpdateCheckResult) => void)[] = [];

  constructor() {
    this.currentVersion = this.getCurrentVersion();
    this.setupUpdateChecks();
  }

  /**
   * Get current app version information
   */
  private getCurrentVersion(): AppVersion {
    return {
      version: import.meta.env.VITE_APP_VERSION || '1.0.0',
      buildDate: import.meta.env.VITE_BUILD_DATE || new Date().toISOString(),
      manifestVersion: localStorage.getItem('piknowkyo-manifest-version') || '1.0.0',
      scriptsVersion: localStorage.getItem('piknowkyo-scripts-version') || '1.0.0',
      assetsVersion: localStorage.getItem('piknowkyo-assets-version') || '1.0.0'
    };
  }

  /**
   * Check for app updates
   */
  async checkForUpdates(force: boolean = false): Promise<UpdateCheckResult | null> {
    const now = Date.now();
    
    // Skip if checked recently (unless forced)
    if (!force && (now - this.lastUpdateCheck) < this.updateCheckInterval) {
      console.log('[Update Service] Skipping update check - checked recently');
      return null;
    }

    this.lastUpdateCheck = now;

    try {
      console.log('[Update Service] Checking for updates...');

      // Check service worker updates
      await serviceWorkerManager.checkForUpdates();

      // Check manifest version
      const manifestUpdate = await this.checkManifestVersion();
      
      if (manifestUpdate) {
        this.notifyUpdateCallbacks(manifestUpdate);
        return manifestUpdate;
      }

      console.log('[Update Service] No updates available');
      return null;

    } catch (error) {
      console.error('[Update Service] Update check failed:', error);
      return null;
    }
  }

  /**
   * Check manifest version for script and asset updates
   */
  private async checkManifestVersion(): Promise<UpdateCheckResult | null> {
    try {
      // Fetch latest manifest with cache-busting
      const response = await fetch(`/version.json?t=${Date.now()}`);
      
      if (!response.ok) {
        console.warn('[Update Service] Version manifest not found, checking fallback');
        return await this.checkFallbackVersion();
      }

      const latestManifest: ManifestVersion = await response.json();
      const currentVersion = this.currentVersion;

      // Compare versions
      const hasUpdate = this.compareVersions(currentVersion, latestManifest);
      
      if (hasUpdate) {
        const updateType = this.determineUpdateType(currentVersion, latestManifest);
        
        const latestVersion: AppVersion = {
          version: latestManifest.version,
          buildDate: latestManifest.buildDate,
          manifestVersion: latestManifest.version,
          scriptsVersion: `${latestManifest.scripts.fr}-${latestManifest.scripts.en}-${latestManifest.scripts.es}`,
          assetsVersion: `${latestManifest.assets.audio}-${latestManifest.assets.images}`
        };

        return {
          hasUpdate: true,
          currentVersion,
          latestVersion,
          updateType,
          requiresReload: updateType === 'major' || updateType === 'minor'
        };
      }

      return null;

    } catch (error) {
      console.error('[Update Service] Manifest version check failed:', error);
      return null;
    }
  }

  /**
   * Fallback version check using manifest.json
   */
  private async checkFallbackVersion(): Promise<UpdateCheckResult | null> {
    try {
      const response = await fetch(`/manifest.json?t=${Date.now()}`);
      
      if (!response.ok) {
        return null;
      }

      const manifest = await response.json();
      const manifestVersion = manifest.version || '1.0.0';
      
      if (manifestVersion !== this.currentVersion.version) {
        return {
          hasUpdate: true,
          currentVersion: this.currentVersion,
          latestVersion: {
            ...this.currentVersion,
            version: manifestVersion
          },
          updateType: 'minor',
          requiresReload: true
        };
      }

      return null;

    } catch (error) {
      console.error('[Update Service] Fallback version check failed:', error);
      return null;
    }
  }

  /**
   * Compare version information
   */
  private compareVersions(current: AppVersion, latest: ManifestVersion): boolean {
    // Check app version
    if (this.isVersionNewer(latest.version, current.version)) {
      return true;
    }

    // Check scripts version
    const latestScriptsVersion = `${latest.scripts.fr}-${latest.scripts.en}-${latest.scripts.es}`;
    if (latestScriptsVersion !== current.scriptsVersion) {
      return true;
    }

    // Check assets version
    const latestAssetsVersion = `${latest.assets.audio}-${latest.assets.images}`;
    if (latestAssetsVersion !== current.assetsVersion) {
      return true;
    }

    return false;
  }

  /**
   * Determine update type based on version changes
   */
  private determineUpdateType(current: AppVersion, latest: ManifestVersion): 'major' | 'minor' | 'patch' | 'assets' | 'scripts' {
    // Check if app version changed
    if (this.isVersionNewer(latest.version, current.version)) {
      const currentParts = current.version.split('.').map(Number);
      const latestParts = latest.version.split('.').map(Number);

      if (latestParts[0] > currentParts[0]) return 'major';
      if (latestParts[1] > currentParts[1]) return 'minor';
      return 'patch';
    }

    // Check scripts version
    const latestScriptsVersion = `${latest.scripts.fr}-${latest.scripts.en}-${latest.scripts.es}`;
    if (latestScriptsVersion !== current.scriptsVersion) {
      return 'scripts';
    }

    // Check assets version
    const latestAssetsVersion = `${latest.assets.audio}-${latest.assets.images}`;
    if (latestAssetsVersion !== current.assetsVersion) {
      return 'assets';
    }

    return 'patch';
  }

  /**
   * Check if version A is newer than version B
   */
  private isVersionNewer(versionA: string, versionB: string): boolean {
    const partsA = versionA.split('.').map(Number);
    const partsB = versionB.split('.').map(Number);

    for (let i = 0; i < Math.max(partsA.length, partsB.length); i++) {
      const a = partsA[i] || 0;
      const b = partsB[i] || 0;

      if (a > b) return true;
      if (a < b) return false;
    }

    return false;
  }

  /**
   * Apply available updates
   */
  async applyUpdate(updateResult: UpdateCheckResult): Promise<boolean> {
    try {
      console.log('[Update Service] Applying update:', updateResult.updateType);

      // Update stored versions
      localStorage.setItem('piknowkyo-manifest-version', updateResult.latestVersion.manifestVersion);
      localStorage.setItem('piknowkyo-scripts-version', updateResult.latestVersion.scriptsVersion);
      localStorage.setItem('piknowkyo-assets-version', updateResult.latestVersion.assetsVersion);

      // Apply service worker update if needed
      if (updateResult.requiresReload) {
        await serviceWorkerManager.applyUpdate();
        return true;
      }

      // For asset-only updates, just clear relevant caches
      if (updateResult.updateType === 'assets' || updateResult.updateType === 'scripts') {
        await this.clearAssetCaches();
        this.currentVersion = updateResult.latestVersion;
        return true;
      }

      return true;

    } catch (error) {
      console.error('[Update Service] Failed to apply update:', error);
      return false;
    }
  }

  /**
   * Clear asset caches for updates
   */
  private async clearAssetCaches(): Promise<void> {
    if (!('caches' in window)) return;

    try {
      const cacheNames = await caches.keys();
      const assetCaches = cacheNames.filter(name => 
        name.includes('assets') || name.includes('scripts')
      );

      await Promise.all(
        assetCaches.map(cacheName => caches.delete(cacheName))
      );

      console.log('[Update Service] Asset caches cleared');
    } catch (error) {
      console.error('[Update Service] Failed to clear asset caches:', error);
    }
  }

  /**
   * Setup automatic update checks
   */
  private setupUpdateChecks(): void {
    // Check on app start
    setTimeout(() => {
      this.checkForUpdates();
    }, 5000); // Wait 5 seconds after app start

    // Check when app becomes visible
    document.addEventListener('visibilitychange', () => {
      if (!document.hidden) {
        this.checkForUpdates();
      }
    });

    // Check when network comes back online
    window.addEventListener('online', () => {
      this.checkForUpdates();
    });

    // Periodic checks
    setInterval(() => {
      this.checkForUpdates();
    }, this.updateCheckInterval);
  }

  /**
   * Register callback for update notifications
   */
  onUpdateAvailable(callback: (result: UpdateCheckResult) => void): void {
    this.updateCallbacks.push(callback);
  }

  /**
   * Notify all registered callbacks
   */
  private notifyUpdateCallbacks(result: UpdateCheckResult): void {
    this.updateCallbacks.forEach(callback => {
      try {
        callback(result);
      } catch (error) {
        console.error('[Update Service] Callback error:', error);
      }
    });
  }

  /**
   * Get current version info
   */
  getCurrentVersionInfo(): AppVersion {
    return { ...this.currentVersion };
  }

  /**
   * Force update check
   */
  async forceUpdateCheck(): Promise<UpdateCheckResult | null> {
    return await this.checkForUpdates(true);
  }
}

// Export singleton instance
export const updateService = new UpdateService();

export default updateService;
