#!/usr/bin/env node

// Production Readiness Test for Piknowkyo
// Tests all critical components and features

const fs = require('fs');
const path = require('path');

console.log('🚀 Piknowkyo Production Readiness Test (Custom)\n');

// Test results
const results = {
  passed: 0,
  failed: 0,
  warnings: 0,
  tests: []
};

function test(name, condition, warning = false) {
  const status = condition ? '✅' : (warning ? '⚠️' : '❌');
  const result = condition ? 'PASS' : (warning ? 'WARN' : 'FAIL');
  
  console.log(`${status} ${name}: ${result}`);
  
  results.tests.push({ name, status: result, warning });
  
  if (condition) {
    results.passed++;
  } else if (warning) {
    results.warnings++;
  } else {
    results.failed++;
  }
}

// Helper function to check if file exists
function fileExists(filePath) {
  return fs.existsSync(path.join(__dirname, filePath));
}

// Helper function to check if file contains content
function fileContains(filePath, content) {
  if (!fileExists(filePath)) return false;
  const fileContent = fs.readFileSync(path.join(__dirname, filePath), 'utf8');
  return fileContent.includes(content);
}

// Helper function to read JSON file
function readJSON(filePath) {
  try {
    return JSON.parse(fs.readFileSync(path.join(__dirname, filePath), 'utf8'));
  } catch {
    return null;
  }
}

console.log('📋 Testing Core Files...\n');

// Test core application files
test('Package.json exists', fileExists('package.json'));
test('Main App component exists', fileExists('src/App.tsx'));
test('Index file exists', fileExists('src/index.tsx'));
test('Firebase config exists', fileExists('src/firebase.ts'));

console.log('\n🔧 Testing Service Worker Implementation...\n');

// Test service worker implementation
test('Service worker file exists', fileExists('public/sw.js'));
test('Service worker manager exists', fileExists('src/services/serviceWorkerManager.ts'));
test('Update service exists', fileExists('src/services/updateService.ts'));
test('Update notification component exists', fileExists('src/components/UpdateNotification.tsx'));
test('Service worker registration in index', fileContains('src/index.tsx', 'serviceWorkerManager'));

console.log('\n💰 Testing Monetization System...\n');

// Test monetization implementation
test('Subscription slice exists', fileExists('src/store/slices/subscriptionSlice.ts'));
test('Ad service exists', fileExists('src/services/adService.ts'));
test('Stripe service exists', fileExists('src/services/stripeService.ts'));
test('Premium gate component exists', fileExists('src/components/PremiumGate.tsx'));
test('Ad reward modal exists', fileExists('src/components/AdRewardModal.tsx'));
test('Subscription hook exists', fileExists('src/hooks/useSubscription.ts'));
test('Ad hook exists', fileExists('src/hooks/useAds.ts'));

console.log('\n📱 Testing PWA Configuration...\n');

// Test PWA setup
const manifest = readJSON('public/manifest.json');
test('PWA manifest exists', manifest !== null);
test('Manifest has Piknowkyo branding', manifest && manifest.name.includes('PiKnowKyo'));
test('Manifest has shortcuts', manifest && manifest.shortcuts && manifest.shortcuts.length > 0);
test('Version tracking file exists', fileExists('public/version.json'));

console.log('\n🗄️ Testing Redux Store...\n');

// Test Redux store configuration
test('Main store file exists', fileExists('src/store/index.ts'));
test('Auth slice exists', fileExists('src/store/slices/authSlice.ts'));
test('Sessions slice exists', fileExists('src/store/slices/sessionsSlice.ts'));
test('Journal slice exists', fileExists('src/store/slices/journalSlice.ts'));
test('Sync slice exists', fileExists('src/store/slices/syncSlice.ts'));
test('Store includes subscription slice', fileContains('src/store/index.ts', 'subscriptionSlice'));

console.log('\n🔄 Testing Offline Capabilities...\n');

// Test offline implementation
test('Cache manager exists', fileExists('src/services/cacheManager.ts'));
test('Sync service exists', fileExists('src/services/syncService.ts'));
test('Network status component exists', fileExists('src/components/NetworkStatusNotifier.tsx'));
test('Sync status indicator exists', fileExists('src/components/SyncStatusIndicator.tsx'));

console.log('\n🎯 Testing Core Components...\n');

// Test essential components
test('Profile page exists', fileExists('src/pages/ProfilePage.tsx'));
test('Profile page uses subscription hooks', fileContains('src/pages/ProfilePage.tsx', 'useSubscription'));
test('Toast component exists', fileExists('src/components/Toast.tsx'));
test('Button component exists', fileExists('src/components/ui/Button.tsx'));
test('Auth page exists', fileExists('src/components/AuthPage.tsx'));

console.log('\n🔐 Testing Security & Configuration...\n');

// Test security and configuration
test('Firestore rules exist', fileExists('firestore.rules'));
test('Storage rules exist', fileExists('storage.rules'));
test('Firebase config exists', fileExists('firebase.json'));
test('TypeScript config exists', fileExists('tsconfig.json'));
test('Vite config exists', fileExists('vite.config.ts'));

console.log('\n📦 Testing Dependencies...\n');

// Test package.json dependencies
const packageJson = readJSON('package.json');
if (packageJson) {
  const deps = { ...packageJson.dependencies, ...packageJson.devDependencies };
  
  test('React installed', deps.react !== undefined);
  test('Redux Toolkit installed', deps['@reduxjs/toolkit'] !== undefined);
  test('Firebase installed', deps.firebase !== undefined);
  test('Styled Components installed', deps['styled-components'] !== undefined);
  test('React Router installed', deps['react-router-dom'] !== undefined);
  test('i18next installed', deps.i18next !== undefined);
  test('Redux Persist installed', deps['redux-persist'] !== undefined);
  test('Stripe JS installed', deps['@stripe/stripe-js'] !== undefined);
  test('LocalForage installed', deps.localforage !== undefined);
}

console.log('\n🌐 Testing Internationalization...\n');

// Test i18n setup
test('i18n config exists', fileExists('src/i18n.ts') || fileExists('src/i18n/index.ts'));
test('French translations exist', fileExists('public/locales/fr/translation.json'));
test('English translations exist', fileExists('public/locales/en/translation.json'));
test('Spanish translations exist', fileExists('public/locales/es/translation.json'));

console.log('\n📊 Testing Assets & Scripts...\n');

// Test assets and scripts
test('Audio manifests exist', fileExists('public/assets/audio_manifests/audio_manifest.json'));
test('Session manifests exist', fileExists('public/assets/manifests/manifest_fr.json'));
test('Build scripts exist', fileExists('scripts/build-sessions.js'));

console.log('\n🔍 Testing Build Configuration...\n');

// Test build configuration
const viteConfigExists = fileExists('vite.config.ts') || fileExists('vite.config.js');
test('Vite config exists', viteConfigExists);
test('Build script in package.json', packageJson && packageJson.scripts && packageJson.scripts.build);
test('Deploy script in package.json', packageJson && packageJson.scripts && packageJson.scripts.deploy);

console.log('\n📈 Testing Performance & Optimization...\n');

// Test performance considerations
test('Lazy loading utilities exist', fileExists('src/utils/lazyLoading.ts'), true);
test('Performance monitor exists', fileExists('src/services/performanceMonitor.ts'), true);
test('Error boundaries exist', fileExists('src/components/ErrorBoundary.tsx'), true);

console.log('\n🧪 Testing Development Tools...\n');

// Test development setup
test('ESLint config exists', fileExists('eslint.config.js') || fileExists('.eslintrc.js'));
test('TypeScript strict mode', fileContains('tsconfig.json', '"strict"'), true);
test('Test script exists', packageJson && packageJson.scripts && packageJson.scripts.test, true);

// Summary
console.log('\n' + '='.repeat(50));
console.log('📊 TEST SUMMARY');
console.log('='.repeat(50));
console.log(`✅ Passed: ${results.passed}`);
console.log(`❌ Failed: ${results.failed}`);
console.log(`⚠️  Warnings: ${results.warnings}`);
console.log(`📋 Total: ${results.tests.length}`);

const passRate = ((results.passed / results.tests.length) * 100).toFixed(1);
console.log(`📈 Pass Rate: ${passRate}%`);

console.log('\n🎯 PRODUCTION READINESS ASSESSMENT:');

if (results.failed === 0 && passRate >= 90) {
  console.log('🟢 EXCELLENT - Ready for production deployment!');
} else if (results.failed <= 2 && passRate >= 80) {
  console.log('🟡 GOOD - Minor issues to address before production');
} else if (results.failed <= 5 && passRate >= 70) {
  console.log('🟠 FAIR - Several issues need attention');
} else {
  console.log('🔴 POOR - Significant work needed before production');
}

// Critical failures
const criticalFailures = results.tests.filter(t => 
  t.status === 'FAIL' && 
  (t.name.includes('Service worker') || 
   t.name.includes('Redux') || 
   t.name.includes('Firebase') ||
   t.name.includes('Subscription'))
);

if (criticalFailures.length > 0) {
  console.log('\n🚨 CRITICAL ISSUES:');
  criticalFailures.forEach(test => {
    console.log(`   ❌ ${test.name}`);
  });
}

// Recommendations
console.log('\n💡 RECOMMENDATIONS:');

if (results.failed > 0) {
  console.log('   • Fix all failed tests before production deployment');
}

if (results.warnings > 0) {
  console.log('   • Address warnings to improve app quality');
}

console.log('   • Run `npm run build` to test production build');
console.log('   • Test offline functionality manually');
console.log('   • Verify Firebase security rules');
console.log('   • Test payment integration in staging environment');
console.log('   • Perform security audit');
console.log('   • Test on multiple devices and browsers');

console.log('\n🚀 Next Steps:');
console.log('   1. Fix any critical failures');
console.log('   2. Run production build: npm run build');
console.log('   3. Test in staging environment');
console.log('   4. Deploy to Firebase: npm run deploy');
console.log('   5. Monitor performance and errors');

process.exit(results.failed > 0 ? 1 : 0);
