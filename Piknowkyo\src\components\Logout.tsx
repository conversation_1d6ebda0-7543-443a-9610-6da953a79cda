// src/components/Logout.tsx
import React from 'react';
import { signOut } from "firebase/auth";
import { auth } from '../firebase';
import { useTranslation } from 'react-i18next'; // Importez useTranslation
import styled from 'styled-components'; // Importez styled-components

// Définition du styled component pour le bouton de déconnexion
const LogoutButton = styled.button`
  background: none; /* Rendre le fond transparent */
  border: none;
  color: ${({ theme }) => theme.text}; /* Utiliser la couleur de texte du thème */
  cursor: pointer;
  font-size: 1.1em; /* Taille de police légèrement plus grande pour un bouton */
  padding: 8px 12px;
  border-radius: 8px; /* Bords légèrement arrondis */
  transition: background-color 0.2s ease, color 0.2s ease; /* Transitions douces */
  display: flex; /* Pour aligner le texte et potentiellement une icône si vous en ajoutez une plus tard */
  align-items: center;
  justify-content: center;
  gap: 5px; /* Espace entre texte et icône si ajoutée */

  &:hover {
    background-color: ${({ theme }) => theme.name === 'light' ? 'rgba(0, 0, 0, 0.05)' : 'rgba(255, 255, 255, 0.1)'};
  }

  &:focus {
    outline: none;
    box-shadow: 0 0 0 2px ${({ theme }) => theme.primary}40; /* Petit focus ring pour l'accessibilité */
  }
`;

const Logout: React.FC = () => {
  const { t } = useTranslation(); // Initialisez useTranslation

  const handleLogout = async () => {
    try {
      await signOut(auth);
      console.log("User logged out successfully");
      // Ici, vous pourriez ajouter un message de confirmation utilisateur si nécessaire.
    } catch (error) {
      console.error("Error logging out:", error);
      // Ici, vous pourriez afficher un message d'erreur à l'utilisateur.
    }
  };

  return (
    <LogoutButton onClick={handleLogout} aria-label={t('auth.logout.button_aria_label')}>
      {t('auth.logout.button')} {/* Utilisation de la clé de traduction */}
    </LogoutButton>
  );
};

export default Logout;