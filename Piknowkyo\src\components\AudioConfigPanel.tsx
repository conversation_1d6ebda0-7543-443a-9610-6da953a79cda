// src/components/AudioConfigPanel.tsx

import React, { useState, useRef, useEffect, useMemo, useContext } from 'react';
import styled, { DefaultTheme, css, ThemeContext } from 'styled-components';
import { ttsPlay, TTSProvider, TTSConfig } from '../services/tts';
import { FiInfo, FiPlay, FiPause, FiVolume2, FiMusic, FiMic, FiRadio, FiMessageCircle } from 'react-icons/fi';
import { SessionAudioConfig } from '../models';
import { useTranslation } from 'react-i18next';

type GrammaticalGender = 'masculine' | 'feminine' | 'neutral';

// --- Interface pour les éléments du manifeste audio ---
interface AudioManifestItem {
  id: string;
  name: string;
  url: string;
  isUserUploaded: boolean;
}

interface AudioManifest {
  musics: AudioManifestItem[];
  ambiants: AudioManifestItem[];
}


// --- Styled Components pour l'organisation ---

const PanelContainer = styled.div`
  /* Conteneur principal du panneau de configuration */
`;

const ConfigSection = styled.div`
  background-color: ${({ theme }) => theme.surfaceAlt};
  border-radius: 10px;
  padding: 1.2rem;
  margin-bottom: 1.5rem;
  border: 1px solid ${({ theme }) => theme.border};

  &:last-child {
    margin-bottom: 0;
  }
`;

// --- Toggle Switch Styled Components ---
const ToggleSwitchContainer = styled.label`
  position: relative;
  display: inline-block;
  width: 48px; // Ajusté pour être un peu plus petit
  height: 24px; // Ajusté
  margin-left: auto; /* Pour pousser le switch à droite */
`;

const ToggleInput = styled.input`
  opacity: 0;
  width: 0;
  height: 0;

  &:checked + .slider {
    background-color: ${({ theme }) => theme.primary};
  }

  &:focus + .slider {
    box-shadow: 0 0 1px ${({ theme }) => theme.primary};
  }

  &:checked + .slider:before {
    transform: translateX(22px); // Ajusté
  }
`;

const ToggleSlider = styled.span`
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: ${({ theme }) => theme.disabled || '#ccc'};
  transition: .4s;
  border-radius: 24px; // Ajusté

  &:before {
    position: absolute;
    content: "";
    height: 18px; // Ajusté
    width: 18px; // Ajusté
    left: 3px; // Ajusté
    bottom: 3px; // Ajusté
    background-color: white;
    transition: .4s;
    border-radius: 50%;
  }
`;

const SectionTitleContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between; /* Pour espacer le titre et le toggle */
  margin-bottom: 1rem;
  border-bottom: 1px solid ${({ theme }) => theme.border};
  padding-bottom: 0.8rem;
  
  h4 {
    font-size: 1.2rem;
    color: ${({ theme }) => theme.primary};
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }
`;

// Le reste de vos styled-components (TooltipWrapper, TooltipIcon, etc.) reste identique
const TooltipWrapper = styled.div`
  position: relative;
  display: inline-flex;
  align-items: center; /* Pour mieux aligner avec le titre */
  margin-left: 0.5rem; /* Espace entre titre et tooltip icon */
`;

const TooltipIcon = styled(FiInfo)<{ $isActive?: boolean }>`
  color: ${({ theme, $isActive }) => $isActive ? theme.primary : theme.textMuted};
  cursor: help;
  font-size: 1rem;
  /* margin-left: 0.5rem; <- déplacé vers TooltipWrapper pour un meilleur contrôle */
  transition: color 0.2s;
  outline: none;

  &:hover + span,
  &:focus + span, /* Ajouter focus pour accessibilité clavier */
  &.active-tooltip + span { 
    visibility: visible;
    opacity: 1;
    z-index: 30;
    transition: opacity 0.3s, visibility 0s linear 0s;
  }
`;

const TooltipText = styled.span<{ $show?: boolean }>`
  visibility: hidden;
  width: 230px;
  background-color: ${({ theme }) => theme.surface};
  color: ${({ theme }) => theme.text};
  text-align: left;
  font-size: 0.85rem;
  font-weight: normal;
  line-height: 1.4;
  border-radius: 6px;
  padding: 0.8rem;
  position: absolute;
  z-index: 20;
  bottom: 140%; 
  left: 50%;
  transform: translateX(-50%);
  opacity: 0;
  transition: opacity 0.3s ease-in-out, visibility 0s linear 0.3s; 
  box-shadow: 0 2px 10px rgba(0,0,0,0.15);
  border: 1px solid ${({ theme }) => theme.border};

  &::after { 
    content: "";
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: ${({ theme }) => theme.surface} transparent transparent transparent;
  }

  ${({ $show }) =>
    $show &&
    css`
      visibility: visible;
      opacity: 1;
      transition-delay: 0s; 
      z-index: 30;
    `}
`;

const InputGroup = styled.div`
  margin-bottom: 1.2rem;
  &:last-child {
    margin-bottom: 0;
  }
  label {
    display: block;
    margin-bottom: 0.4rem;
    font-weight: 500;
    color: ${({ theme }) => theme.text};
    font-size: 0.95rem;
  }
  input[type="range"] {
    width: 100%;
    margin-bottom: 0.2rem;
  }
  select {
    width: 100%;
    padding: 0.7rem;
    border-radius: 8px;
    border: 1px solid ${({ theme }) => theme.border || '#ddd'};
    background: ${({ theme }) => theme.inputBackground || '#fff'};
    color: ${({ theme }) => theme.text};
    margin-top: 0.3rem;
    font-size: 0.95rem;
    appearance: none;
    background-image: url('data:image/svg+xml;charset=US-ASCII,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%22292.4%22%20height%3D%22292.4%22%3E%3Cpath%20fill%3D%22%23${({ theme }) => (theme.textSecondary || '6c757d').substring(1)}%22%20d%3D%22M287%2069.4a17.6%2017.6%200%200%200-13-5.4H18.4c-5%200-9.3%201.8-12.9%205.4A17.6%2017.6%200%200%200%200%2082.2c0%205%201.8%209.3%205.4%2012.9l128%20127.9c3.6%203.6%207.8%205.4%2012.8%205.4s9.2-1.8%2012.8-5.4L287%2095c3.5-3.5%205.4-7.8%205.4-12.8%200-5-1.9-9.2-5.5-12.8z%22%2F%3E%3C%2Fsvg%3E');
    background-repeat: no-repeat;
    background-position: right 0.7em top 50%, 0 0;
    background-size: 0.65em auto, 100%;
    padding-right: 2.5em;
  }
  .preset-desc { /* Utilisé pour afficher la description du preset sélectionné */
    font-size: 0.85em;
    color: ${({ theme }) => theme.textMuted || '#666'};
    margin-top: 0.3rem;
    min-height: 1.2em; 
  }
  .info-text {
    font-size: 0.85rem;
    color: ${({ theme }) => theme.textMuted || '#777'};
    margin-left: 0.5rem;
    display: block;
    margin-top: 0.5rem;
  }
`;

const Button = styled.button`
  background: ${({ theme }) => theme.primary};
  color: ${({ theme }) => theme.textLight || '#fff'}; 
  border: none;
  border-radius: 8px;
  padding: 0.7rem 1.5rem;
  font-size: 1rem;
  font-weight: 500;
  margin-top: 1rem;
  cursor: pointer;
  transition: background-color 0.2s ease-in-out, transform 0.1s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;

  &:hover {
    opacity: 0.9;
  }
  &:active {
    transform: scale(0.98);
  }

  &:disabled {
    background: ${({ theme }) => theme.disabled || '#ccc'};
    color: ${({ theme }) => theme.textMuted || '#666'};
    cursor: not-allowed;
  }
`;

const InfoTextSmall = styled.p`
  font-size: 0.85em;
  color: ${({ theme }) => theme.textMuted || '#666'};
  margin-top: 0.3rem;
  min-height: 1.2em; 
`;

// --- Section Title with Toggle ---
interface SectionTitleWithToggleProps {
  icon: React.ReactNode;
  title: string;
  tooltipText: string;
  tooltipId: string;
  isChecked: boolean;
  onToggle: (checked: boolean) => void;
}

const SectionHeader: React.FC<SectionTitleWithToggleProps> = ({
  icon, title, tooltipText, tooltipId, isChecked, onToggle
}) => (
  <SectionTitleContainer>
    <div style={{ display: 'flex', alignItems: 'center' }}>
      <h4>{icon} {title}</h4>
      <InfoTooltip id={tooltipId} text={tooltipText} />
    </div>
    <ToggleSwitchContainer htmlFor={`toggle-${tooltipId}`}>
      <ToggleInput
        id={`toggle-${tooltipId}`}
        type="checkbox"
        checked={isChecked}
        onChange={(e) => onToggle(e.target.checked)}
        aria-label={`Activer/Désactiver ${title}`}
      />
      <ToggleSlider className="slider" />
    </ToggleSwitchContainer>
  </SectionTitleContainer>
);


// --- Enums et Descriptions (inchangés) ---
enum ChakraFrequency { ROOT = 396, SACRAL = 417, SOLAR_PLEXUS = 528, HEART = 639, THROAT = 741, THIRD_EYE = 852, CROWN = 963, LOWER_EARTH_STAR = 174, SOUL_STAR = 285 }
const chakraDescriptions: Record<keyof typeof ChakraFrequency, string> = { ROOT: 'Libération peur/culpabilité, Sécurité, Stabilité.', SACRAL: 'Facilitation changement, Créativité, Sexualité.', SOLAR_PLEXUS: 'Transformation, Miracles (ADN), Confiance.', HEART: 'Connexion, Relations, Amour, Compassion.', THROAT: 'Expression de soi, Communication.', THIRD_EYE: 'Intuition, Clairvoyance.', CROWN: 'Connexion spirituelle, Illumination.', LOWER_EARTH_STAR: 'Ancrage profond, Soulagement douleur.', SOUL_STAR: 'Restauration tissus, Cognition quantique.' };
enum OrganAndSystemFrequency { LIVER = 95, KIDNEYS = 319, LUNGS = 220, HEART_ORGAN = 128, PINEAL_GLAND = 480, THYROID = 295, ADRENALS = 492, BRAIN = 315.8, STOMACH = 110, PANCREAS = 117, SPLEEN = 380, GALLBLADDER = 300, SMALL_INTESTINE = 281, LARGE_INTESTINE = 176, BLADDER = 352, BONES = 40, MUSCLES = 132, SKIN = 20, BLOOD = 50, IMMUNE_SYSTEM_GENERAL = 8, NERVOUS_SYSTEM = 7.83, LYMPHATIC_SYSTEM = 15, PITUITARY_GLAND = 636, THYMUS = 384 }
const organSystemDescriptions: Record<keyof typeof OrganAndSystemFrequency, string> = { LIVER: 'Foie - Détoxification.', KIDNEYS: 'Reins - Filtration.', LUNGS: 'Poumons - Respiration.', HEART_ORGAN: 'Cœur (organe) - Circulation.', PINEAL_GLAND: 'Glande pinéale - Intuition.', THYROID: 'Thyroïde - Métabolisme.', ADRENALS: 'Surrénales - Énergie.', BRAIN: 'Cerveau - Activité mentale.', STOMACH: 'Estomac - Digestion.', PANCREAS: 'Pancréas - Glycémie.', SPLEEN: 'Rate - Immunité.', GALLBLADDER: 'Vésicule biliaire - Digestion graisses.', SMALL_INTESTINE: 'Intestin grêle - Absorption.', LARGE_INTESTINE: 'Gros intestin - Élimination.', BLADDER: 'Vessie - Urine.', BONES: 'Os - Croissance.', MUSCLES: 'Muscles - Tonus.', SKIN: 'Peau - Régénération.', BLOOD: 'Sang - Circulation.', IMMUNE_SYSTEM_GENERAL: 'Syst. immunitaire.', NERVOUS_SYSTEM: 'Syst. nerveux (Schumann).', LYMPHATIC_SYSTEM: 'Syst. lymphatique.', PITUITARY_GLAND: 'Pituitaire - Hormones.', THYMUS: 'Thymus - Immunité.' };
enum PlanetaryFrequency { SUN = 126.22, MOON_SYNODIC = 210.42, EARTH_DAY = 194.18, EARTH_YEAR_OM = 136.10, MERCURY = 141.27, VENUS = 221.23, MARS = 144.72, JUPITER = 183.58, SATURN = 147.85, URANUS = 207.36, NEPTUNE = 211.44, PLUTO = 140.25 }
const planetaryDescriptions: Record<keyof typeof PlanetaryFrequency, string> = { SUN: 'Soleil - Vitalité.', MOON_SYNODIC: 'Lune - Émotions.', EARTH_DAY: 'Terre (jour) - Ancrage.', EARTH_YEAR_OM: 'Terre (OM) - Relaxation.', MERCURY: 'Mercure - Communication.', VENUS: 'Vénus - Amour, Harmonie.', MARS: 'Mars - Énergie.', JUPITER: 'Jupiter - Croissance.', SATURN: 'Saturne - Structure.', URANUS: 'Uranus - Inspiration.', NEPTUNE: 'Neptune - Intuition.', PLUTO: 'Pluton - Transformation.' };
enum BrainwaveBeatFrequency { DELTA_LOW = 1, THETA_LOW = 4, ALPHA_MID = 10, BETA_LOW = 12, BETA_MID = 20, BETA_HIGH = 30, GAMMA_LOW = 38, SCHUMANN_RESONANCE = 7.83 }
const brainwaveDescriptions: Record<keyof typeof BrainwaveBeatFrequency, string> = { DELTA_LOW: 'Delta (0.5-4 Hz) - Sommeil profond.', THETA_LOW: 'Theta (4-8 Hz) - Méditation, Créativité.', ALPHA_MID: 'Alpha (8-12 Hz) - Relaxation, Apprentissage.', BETA_LOW: 'Bêta Bas (12-15 Hz) - Attention détendue.', BETA_MID: 'Bêta Moyen (15-22 Hz) - Concentration.', BETA_HIGH: 'Bêta Haut (22-38 Hz) - Forte concentration.', GAMMA_LOW: 'Gamma (38+ Hz) - Hautes performances.', SCHUMANN_RESONANCE: 'Résonance Schumann (7.83 Hz beat) - Équilibre.' };
enum OtherNotableFrequency { A4_CONCERT_PITCH = 440, A4_VERDI_TUNING = 432, RIFE_UNIVERSAL_1 = 727, RIFE_UNIVERSAL_2 = 787, RIFE_UNIVERSAL_3 = 880, FREQUENCY_OF_CREATION = 432, LOVE_FREQUENCY = 528 }
const otherNotableDescriptions: Record<keyof typeof OtherNotableFrequency, string> = { A4_CONCERT_PITCH: 'La standard (440 Hz).', A4_VERDI_TUNING: 'La de Verdi (432 Hz) - Harmonisant.', RIFE_UNIVERSAL_1: 'Rife Univ. 1 (727 Hz).', RIFE_UNIVERSAL_2: 'Rife Univ. 2 (787 Hz).', RIFE_UNIVERSAL_3: 'Rife Univ. 3 (880 Hz).', FREQUENCY_OF_CREATION: 'Création (432 Hz) - Harmonie.', LOVE_FREQUENCY: 'Amour (528 Hz) - Plexus Solaire.' };

interface PresetOption {
  key: string;
  name: string;
  value: number;
  originalKey: string;
  type: 'chakra' | 'organ' | 'planetary' | 'other' | 'brainwave';
}
interface OptGroupData { label: string; options: PresetOption[]; }
function formatEnumKey(key: string): string { return key.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()).join(' '); }

interface AudioConfigPanelProps {
  initialConfig?: SessionAudioConfig;
  onConfigChange: (newConfig: SessionAudioConfig) => void;
}

const InfoTooltip: React.FC<{ text: string; children?: React.ReactNode; id: string }> = ({ text, id }) => {
    const [showTooltipState, setShowTooltipState] = useState(false);
    const tooltipRef = useRef<HTMLDivElement>(null);
    const handleClick = (event: React.MouseEvent | React.KeyboardEvent) => {
        event.stopPropagation();
        setShowTooltipState(prev => !prev);
    };
    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (tooltipRef.current && !tooltipRef.current.contains(event.target as Node)) {
                setShowTooltipState(false);
            }
        };
        if (showTooltipState) {
            document.addEventListener('mousedown', handleClickOutside);
        } else {
            document.removeEventListener('mousedown', handleClickOutside);
        }
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, [showTooltipState]);
    return (
        <TooltipWrapper ref={tooltipRef}>
            <TooltipIcon
                onClick={handleClick}
                onKeyDown={(e) => { if (e.key === 'Enter' || e.key === ' ') { e.preventDefault(); handleClick(e); }}}
                $isActive={showTooltipState}
                aria-describedby={`tooltip-text-${id}`}
                tabIndex={0} 
            />
            <TooltipText id={`tooltip-text-${id}`} role="tooltip" $show={showTooltipState}>{text}</TooltipText>
        </TooltipWrapper>
    );
};

const AudioConfigPanel: React.FC<AudioConfigPanelProps> = ({ initialConfig, onConfigChange }) => {
  const { t } = useTranslation();
  const theme = useContext(ThemeContext) as DefaultTheme;

  const [enableMusic, setEnableMusic] = useState(initialConfig?.enableMusic ?? false);
  const [musicVolume, setMusicVolume] = useState(initialConfig?.music?.volume ?? 0.5);
  const [musicFileUrl, setMusicFileUrl] = useState(initialConfig?.music?.url || '');

  const [enableAmbient, setEnableAmbient] = useState(initialConfig?.enableAmbient ?? false);
  const [ambientVolume, setAmbientVolume] = useState(initialConfig?.ambient?.volume ?? 0.3);
  const [ambientFileUrl, setAmbientFileUrl] = useState(initialConfig?.ambient?.url || '');

  const [enableBinaural, setEnableBinaural] = useState(initialConfig?.enableBinaural ?? false);
  const [binauralVolume, setBinauralVolume] = useState(initialConfig?.binaural?.volume ?? 0.2);
  const [baseFreq, setBaseFreq] = useState(initialConfig?.binaural?.baseFreq ?? 100);
  const [beatFreq, setBeatFreq] = useState(initialConfig?.binaural?.beatFreq ?? 10); 
  
  const [voiceVolume, setVoiceVolume] = useState(initialConfig?.voice?.volume ?? 1);
  const [voiceGender, setVoiceGender] = useState<GrammaticalGender | 'auto'>(
    initialConfig?.voice?.gender === undefined ? 'auto' : initialConfig.voice.gender
  );

  // State pour les options chargées depuis le manifeste
  const [musicManifestOptions, setMusicManifestOptions] = useState<AudioManifestItem[]>([]);
  const [ambientManifestOptions, setAmbientManifestOptions] = useState<AudioManifestItem[]>([]);

  // Charger le manifeste audio
  useEffect(() => {
    fetch('/assets/audio_manifests/audio_manifest.json') // <--- CORRECTION ICI
      .then(response => {
        if (!response.ok) {
          // Pour un débogage plus précis, vous pouvez logger le statut ici
          console.error(`Failed to fetch manifest: ${response.status} ${response.statusText}`);
          // Vous pouvez aussi tenter de lire la réponse comme texte pour voir ce qui est retourné
          response.text().then(text => console.error("Response body:", text.substring(0, 200))); // Affiche les 200 premiers caractères
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
      })
      .then((data: AudioManifest) => {
        setMusicManifestOptions([
          { id: 'none-music', name: t('audioConfig.music.none', 'Aucune'), url: '', isUserUploaded: false },
          ...data.musics
        ]);
        setAmbientManifestOptions([
          { id: 'none-ambient', name: t('audioConfig.ambient.none', 'Aucun'), url: '', isUserUploaded: false },
          ...data.ambiants
        ]);
      })
      .catch(error => {
        console.error("Could not load audio manifest:", error);
        setMusicManifestOptions([{ id: 'none-music', name: t('audioConfig.music.none', 'Aucune'), url: '', isUserUploaded: false }]);
        setAmbientManifestOptions([{ id: 'none-ambient', name: t('audioConfig.ambient.none', 'Aucun'), url: '', isUserUploaded: false }]);
      });
  }, [t]);

  const musicOptions = useMemo(() => 
    musicManifestOptions.map(item => ({ label: item.name, value: item.url, id: item.id }))
  , [musicManifestOptions]);

  const ambientOptions = useMemo(() => 
    ambientManifestOptions.map(item => ({ label: item.name, value: item.url, id: item.id }))
  , [ambientManifestOptions]);


  const musicAudioRef = useRef<HTMLAudioElement | null>(null);
  const ambientAudioRef = useRef<HTMLAudioElement | null>(null);
  
  const [selectedBasePresetKey, setSelectedBasePresetKey] = useState<string | null>(null);
  const [selectedBrainwavePresetKey, setSelectedBrainwavePresetKey] = useState<string | null>(null);

  const audioCtxRef = useRef<AudioContext | null>(null);
  const binauralOscs = useRef<{left?: OscillatorNode, right?: OscillatorNode, gain?: GainNode}>({});
  const [binauralPlaying, setBinauralPlaying] = useState(false);

  const generatePresetOptions = <T extends Record<string, number | string>>(
    enumObj: T, descriptions: Record<keyof T, string>, type: PresetOption['type'], prefix: string
  ): PresetOption[] => {
    return Object.keys(enumObj)
      .filter(key => isNaN(Number(key)))
      .map(originalKey => {
        const value = Number(enumObj[originalKey as keyof T]);
        const desc = descriptions[originalKey as keyof T] || t('audioConfig.noDescription', 'Pas de description.');
        return { key: `${prefix}_${originalKey}`, name: `${formatEnumKey(originalKey)} (${value} Hz) - ${desc}`, value, originalKey, type };
      });
  };
  
  const allBaseFreqOptGroups: OptGroupData[] = useMemo(() => [
    { label: t('audioConfig.presets.chakras', "Chakras (Solfeggio)"), options: generatePresetOptions(ChakraFrequency, chakraDescriptions, 'chakra', 'CHAKRA') },
    { label: t('audioConfig.presets.organs', "Organes & Systèmes"), options: generatePresetOptions(OrganAndSystemFrequency, organSystemDescriptions, 'organ', 'ORGAN') },
    { label: t('audioConfig.presets.planets', "Fréquences Planétaires"), options: generatePresetOptions(PlanetaryFrequency, planetaryDescriptions, 'planetary', 'PLANETARY') },
    { label: t('audioConfig.presets.otherNotable', "Autres Fréquences Notables"), options: generatePresetOptions(OtherNotableFrequency, otherNotableDescriptions, 'other', 'OTHER') },
  ], [t]);

  const brainwavePresetOptions: PresetOption[] = useMemo(() => 
    generatePresetOptions(BrainwaveBeatFrequency, brainwaveDescriptions, 'brainwave', 'BRAINWAVE')
      .map(p => ({...p, name: `${formatEnumKey(p.originalKey)} (${p.value} Hz beat) - ${brainwaveDescriptions[p.originalKey as keyof typeof BrainwaveBeatFrequency]}`}))
  , [t]);

  useEffect(() => {
    const newConfig: SessionAudioConfig = {
      enableMusic,
      music: enableMusic && musicFileUrl ? { url: musicFileUrl, volume: musicVolume } : undefined,
      enableAmbient,
      ambient: enableAmbient && ambientFileUrl ? { url: ambientFileUrl, volume: ambientVolume } : undefined,
      enableBinaural,
      binaural: enableBinaural ? { volume: binauralVolume, baseFreq: baseFreq, beatFreq: beatFreq } : undefined,
      voice: { volume: voiceVolume, gender: voiceGender === 'auto' ? undefined : voiceGender },
    };
    onConfigChange(newConfig);
  }, [
    enableMusic, musicFileUrl, musicVolume,
    enableAmbient, ambientFileUrl, ambientVolume,
    enableBinaural, binauralVolume, baseFreq, beatFreq,
    voiceVolume, voiceGender, onConfigChange
  ]);

  const handleTestVoice = async () => {
    let ttsConfigFromStorage = { provider: 'browser' as TTSProvider , voice: 'auto', lang: 'fr' };
    try {
      const saved = localStorage.getItem('tts_config_v2'); 
      if (saved) ttsConfigFromStorage = { ...ttsConfigFromStorage, ...JSON.parse(saved) };
    } catch (e) { console.error("Erreur de parsing de la config TTS:", e); }
    
    const testText = t('audioConfig.testVoiceText', 'Ceci est un test de la voix.');
    const currentTTSConfig: TTSConfig = { volume: voiceVolume };
    
    try {
        await ttsPlay(ttsConfigFromStorage.provider, testText, ttsConfigFromStorage.voice, ttsConfigFromStorage.lang, currentTTSConfig);
    } catch (e) {
        alert(t('audioConfig.testVoiceError', 'Erreur lors du test TTS : {{message}}', { message: (e instanceof Error) ? e.message : String(e) }));
    }
  };

  const handleMusicSelect = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newFileUrl = e.target.value;
    setMusicFileUrl(newFileUrl);
    if (musicAudioRef.current) {
      if (newFileUrl === '') {
        musicAudioRef.current.pause();
        musicAudioRef.current.currentTime = 0;
        musicAudioRef.current.src = '';
      } else {
        musicAudioRef.current.src = newFileUrl;
        musicAudioRef.current.load();
      }
    }
  };

  const handleAmbientSelect = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newFileUrl = e.target.value;
    setAmbientFileUrl(newFileUrl);
     if (ambientAudioRef.current) {
      if (newFileUrl === '') {
        ambientAudioRef.current.pause();
        ambientAudioRef.current.currentTime = 0;
        ambientAudioRef.current.src = '';
      } else {
        ambientAudioRef.current.src = newFileUrl;
        ambientAudioRef.current.load();
      }
    }
  };

  useEffect(() => { if (musicAudioRef.current) musicAudioRef.current.volume = musicVolume; }, [musicVolume]);
  useEffect(() => { if (ambientAudioRef.current) ambientAudioRef.current.volume = ambientVolume; }, [ambientVolume]);
  
  useEffect(() => {
    if (binauralPlaying && binauralOscs.current?.gain && audioCtxRef.current) {
      binauralOscs.current.gain.gain.setValueAtTime(binauralVolume, audioCtxRef.current.currentTime);
    }
  }, [binauralVolume, binauralPlaying]);

  const stopBinauralSound = () => {
    if (audioCtxRef.current) {
        try {
            binauralOscs.current.left?.stop();
            binauralOscs.current.right?.stop();
            if (audioCtxRef.current.state !== 'closed') {
                audioCtxRef.current.close().catch(e => console.warn("Error closing AudioContext:", e));
            }
        } catch (e) { console.warn("Error stopping/closing binaural audio:", e); }
        audioCtxRef.current = null;
        binauralOscs.current = {};
    }
    setBinauralPlaying(false);
  };

  const handleTestBinaural = () => {
    if (binauralPlaying) {
      stopBinauralSound();
      return;
    }
    if (!window.AudioContext && !(window as any).webkitAudioContext) {
      alert(t('audioConfig.webAudioNotSupported', 'Web Audio API non supportée par votre navigateur.'));
      return;
    }
    if (audioCtxRef.current) stopBinauralSound();

    setTimeout(() => {
        const Ctx = window.AudioContext || (window as any).webkitAudioContext;
        const newCtx = new Ctx();
        audioCtxRef.current = newCtx;
        
        const gainNode = newCtx.createGain();
        gainNode.gain.setValueAtTime(binauralVolume, newCtx.currentTime);
        gainNode.connect(newCtx.destination);
        
        const left = newCtx.createOscillator();
        const right = newCtx.createOscillator();
        left.type = right.type = 'sine';
        left.frequency.setValueAtTime(baseFreq, newCtx.currentTime);
        right.frequency.setValueAtTime(baseFreq + beatFreq, newCtx.currentTime);
        
        const merger = newCtx.createChannelMerger(2);
        left.connect(merger, 0, 0);
        right.connect(merger, 0, 1);
        merger.connect(gainNode);
        
        left.start();
        right.start();
        binauralOscs.current = { left, right, gain: gainNode };
        setBinauralPlaying(true);
    }, 100);
  };
  
  useEffect(() => { 
    return () => { 
      stopBinauralSound(); 
      if (musicAudioRef.current) { musicAudioRef.current.pause(); musicAudioRef.current.src = ''; }
      if (ambientAudioRef.current) { ambientAudioRef.current.pause(); ambientAudioRef.current.src = ''; }
    }; 
  }, []);
  
  const updateBinauralFrequencies = (newBaseFreq: number, newBeatFreq: number) => {
    if (binauralPlaying && audioCtxRef.current && binauralOscs.current.left && binauralOscs.current.right) {
        const now = audioCtxRef.current.currentTime;
        binauralOscs.current.left.frequency.setValueAtTime(newBaseFreq, now);
        binauralOscs.current.right.frequency.setValueAtTime(newBaseFreq + newBeatFreq, now);
    }
  };
  
  const handleBaseFreqSliderChange = (newFreq: number) => {
    setBaseFreq(newFreq);
    setSelectedBasePresetKey(null);
    updateBinauralFrequencies(newFreq, beatFreq);
  };

  const handleBeatFreqSliderChange = (newFreq: number) => {
    setBeatFreq(newFreq);
    setSelectedBrainwavePresetKey(null);
    updateBinauralFrequencies(baseFreq, newFreq);
  };

  const handleBasePresetSelect = (event: React.ChangeEvent<HTMLSelectElement>) => {
    const selectedKey = event.target.value;
    setSelectedBasePresetKey(selectedKey);
    if (selectedKey) {
      const foundPreset = allBaseFreqOptGroups.flatMap(g => g.options).find(p => p.key === selectedKey);
      if (foundPreset) {
        setBaseFreq(foundPreset.value);
        updateBinauralFrequencies(foundPreset.value, beatFreq);
      }
    }
  };
  
  const handleBrainwavePresetSelect = (event: React.ChangeEvent<HTMLSelectElement>) => {
    const selectedKey = event.target.value;
    setSelectedBrainwavePresetKey(selectedKey);
    if (selectedKey) {
      const foundPreset = brainwavePresetOptions.find(p => p.key === selectedKey);
      if (foundPreset) {
        setBeatFreq(foundPreset.value);
        updateBinauralFrequencies(baseFreq, foundPreset.value);
      }
    }
  };

  return (
    <PanelContainer>
      {/* Section Musique */}
      <ConfigSection>
        <SectionHeader
          icon={<FiMusic />}
          title={t('audioConfig.musicTitle', 'Musique de Fond')}
          tooltipText={t('audioConfig.musicTooltip', "Choisissez une musique d'ambiance pour accompagner votre séance. Vous pouvez ajuster le volume.")}
          tooltipId="music"
          isChecked={enableMusic}
          onToggle={setEnableMusic}
        />
        {enableMusic && (
            <>
                <InputGroup>
                <label htmlFor="music-select">{t('audioConfig.musicTrack', 'Piste musicale :')}</label>
                <select id="music-select" value={musicFileUrl} onChange={handleMusicSelect}>
                    {musicOptions.map(opt => <option key={opt.id} value={opt.value}>{opt.label}</option>)}
                </select>
                {musicFileUrl && (
                    <audio ref={musicAudioRef} src={musicFileUrl} controls loop style={{ display: 'block', marginTop: '0.75rem', width: '100%' }} />
                )}
                </InputGroup>
                <InputGroup>
                <label htmlFor="music-volume">{t('audioConfig.volume', 'Volume musique')} : {Math.round(musicVolume * 100)}%</label>
                <input id="music-volume" type="range" min={0} max={1} step={0.01} value={musicVolume} onChange={e => setMusicVolume(Number(e.target.value))} />
                </InputGroup>
            </>
        )}
      </ConfigSection>

      {/* Section Ambiance */}
      <ConfigSection>
         <SectionHeader
          icon={<FiRadio />}
          title={t('audioConfig.ambientTitle', 'Sons Ambiants')}
          tooltipText={t('audioConfig.ambientTooltip', "Ajoutez des sons de la nature ou des bruits blancs pour une immersion totale.")}
          tooltipId="ambient"
          isChecked={enableAmbient}
          onToggle={setEnableAmbient}
        />
        {enableAmbient && (
            <>
                <InputGroup>
                <label htmlFor="ambient-select">{t('audioConfig.ambientSound', 'Ambiance sonore :')}</label>
                <select id="ambient-select" value={ambientFileUrl} onChange={handleAmbientSelect}>
                    {ambientOptions.map(opt => <option key={opt.id} value={opt.value}>{opt.label}</option>)}
                </select>
                {ambientFileUrl && (
                    <audio ref={ambientAudioRef} src={ambientFileUrl} controls loop style={{ display: 'block', marginTop: '0.75rem', width: '100%' }} />
                )}
                </InputGroup>
                <InputGroup>
                <label htmlFor="ambient-volume">{t('audioConfig.volume', 'Volume ambiance')} : {Math.round(ambientVolume * 100)}%</label>
                <input id="ambient-volume" type="range" min={0} max={1} step={0.01} value={ambientVolume} onChange={e => setAmbientVolume(Number(e.target.value))} />
                </InputGroup>
            </>
        )}
      </ConfigSection>
      
      {/* Section Binauraux */}
      <ConfigSection>
        <SectionHeader
            icon={<FiVolume2 />}
            title={t('audioConfig.binauralBeats', 'Sons Binauraux / Isochrones')}
            tooltipText={t('audioConfig.binauralTooltip', "Générez des sons pour influencer les ondes cérébrales. Nécessite un casque pour l'effet binaural optimal.")}
            tooltipId="binaural"
            isChecked={enableBinaural}
            onToggle={setEnableBinaural}
        />
        {enableBinaural && (
            <>
                <InputGroup>
                <label htmlFor="base-freq-slider">{t('audioConfig.baseFrequency', 'Fréquence de base (Hz)')} : {baseFreq.toFixed(1)}</label>
                <input id="base-freq-slider" type="range" min={30} max={500} step={1} value={baseFreq} onChange={e => handleBaseFreqSliderChange(Number(e.target.value))} />
                <label htmlFor="base-freq-preset">{t('audioConfig.baseFreqPresets', 'Préréglages Fréq. de Base :')}</label>
                <select id="base-freq-preset" value={selectedBasePresetKey ?? ''} onChange={handleBasePresetSelect}>
                    <option value="">{t('audioConfig.selectPreset', '-- Choisir un préréglage --')}</option>
                    {allBaseFreqOptGroups.map(group => (
                    <optgroup label={group.label} key={group.label}>
                        {group.options.map(preset => ( <option key={preset.key} value={preset.key}>{preset.name}</option> ))}
                    </optgroup>
                    ))}
                </select>
                </InputGroup>
                <InputGroup>
                <label htmlFor="beat-freq-slider">{t('audioConfig.beatFrequency', 'Battement (Hz)')} : {beatFreq.toFixed(1)}</label>
                <input id="beat-freq-slider" type="range" min={0.5} max={30} step={0.1} value={beatFreq} onChange={e => handleBeatFreqSliderChange(Number(e.target.value))} />
                <label htmlFor="beat-freq-preset">{t('audioConfig.brainwavePresets', 'Préréglages Ondes Cérébrales (Battement) :')}</label>
                <select id="beat-freq-preset" value={selectedBrainwavePresetKey ?? ''} onChange={handleBrainwavePresetSelect}>
                    <option value="">{t('audioConfig.selectState', '-- Choisir un état --')}</option>
                    {brainwavePresetOptions.map(preset => ( <option key={preset.key} value={preset.key}>{preset.name}</option> ))}
                </select>
                <InfoTextSmall>{t('audioConfig.targetFrequencyInfo', 'Oreille G: {{leftEar}} Hz, Oreille D: {{rightEar}} Hz', { leftEar: baseFreq.toFixed(1), rightEar: (baseFreq + beatFreq).toFixed(1) })}</InfoTextSmall>
                </InputGroup>
                <InputGroup>
                    <label htmlFor="binaural-volume">{t('audioConfig.volume', 'Volume binaural')} : {Math.round(binauralVolume * 100)}%</label>
                    <input id="binaural-volume" type="range" min={0} max={1} step={0.01} value={binauralVolume} onChange={e => setBinauralVolume(Number(e.target.value))} />
                </InputGroup>
                <Button onClick={handleTestBinaural} style={{background: binauralPlaying ? theme.errorColor || '#e74c3c' : undefined}}>
                {binauralPlaying ? <><FiPause /> {t('actions.stopTest', 'Arrêter Test')}</> : <><FiPlay /> {t('actions.testSound', 'Tester Son')}</>}
                </Button>
                <span className="info-text">({t('audioConfig.headphonesRequired', 'Nécessite un casque pour l\'effet binaural')})</span>
            </>
        )}
      </ConfigSection>

      {/* Section Voix */}
      <ConfigSection>
        <SectionTitleContainer>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <h4><FiMic/> {t('audioConfig.ttsTitle', 'Synthèse Vocale')}</h4>
            <InfoTooltip 
              id="tts-tooltip"
              text={t('audioConfig.ttsTooltip', "Ajustez le volume de la voix du guide. Le type de voix et la langue sont gérés dans les paramètres généraux de l'application.")} 
            />
          </div>
          {/* Pas de toggle pour la voix, elle est toujours activable par le système */}
        </SectionTitleContainer>
        <InputGroup>
            <label htmlFor="voice-volume">{t('audioConfig.volume', 'Volume voix')} : {Math.round(voiceVolume * 100)}%</label>
            <input id="voice-volume" type="range" min={0} max={1} step={0.01} value={voiceVolume} onChange={e => setVoiceVolume(Number(e.target.value))} />
        </InputGroup>

        <Button onClick={handleTestVoice}><FiMessageCircle/> {t('actions.testVoice', 'Tester la Voix')}</Button>
      </ConfigSection>
    </PanelContainer>
  );
};

export default AudioConfigPanel;