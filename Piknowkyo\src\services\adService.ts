// Ad Service for Piknowkyo
// Handles Google AdMob integration and rewarded ads for premium feature unlocks

export interface AdConfig {
  rewardedAdUnitId: string;
  interstitialAdUnitId: string;
  bannerAdUnitId: string;
  testMode: boolean;
}

export interface AdReward {
  type: 'premium_unlock';
  duration: number; // in milliseconds
  features: string[];
  timestamp: number;
}

export interface AdLoadResult {
  success: boolean;
  error?: string;
  adId?: string;
}

export interface AdShowResult {
  success: boolean;
  rewarded: boolean;
  reward?: AdReward;
  error?: string;
}

class AdService {
  private config: AdConfig;
  private isInitialized = false;
  private loadedAds: Map<string, any> = new Map();
  private rewardCallbacks: ((reward: AdReward) => void)[] = [];

  constructor() {
    this.config = {
      rewardedAdUnitId: import.meta.env.VITE_ADMOB_REWARDED_AD_UNIT_ID || 'ca-app-pub-3940256099942544/5224354917', // Test ID
      interstitialAdUnitId: import.meta.env.VITE_ADMOB_INTERSTITIAL_AD_UNIT_ID || 'ca-app-pub-3940256099942544/1033173712', // Test ID
      bannerAdUnitId: import.meta.env.VITE_ADMOB_BANNER_AD_UNIT_ID || 'ca-app-pub-3940256099942544/**********', // Test ID
      testMode: import.meta.env.NODE_ENV !== 'production'
    };
  }

  /**
   * Initialize the ad service
   */
  async initialize(): Promise<boolean> {
    if (this.isInitialized) {
      return true;
    }

    try {
      console.log('[Ad Service] Initializing...');

      // For web platform, we'll simulate ad functionality
      // In a real implementation, you'd integrate with Google AdSense or another web ad provider
      if (this.isWebPlatform()) {
        await this.initializeWebAds();
      } else {
        // For mobile platforms, use Capacitor AdMob plugin
        await this.initializeMobileAds();
      }

      this.isInitialized = true;
      console.log('[Ad Service] Initialized successfully');
      return true;

    } catch (error) {
      console.error('[Ad Service] Initialization failed:', error);
      return false;
    }
  }

  /**
   * Load a rewarded ad
   */
  async loadRewardedAd(): Promise<AdLoadResult> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    try {
      console.log('[Ad Service] Loading rewarded ad...');

      if (this.isWebPlatform()) {
        return await this.loadWebRewardedAd();
      } else {
        return await this.loadMobileRewardedAd();
      }

    } catch (error) {
      console.error('[Ad Service] Failed to load rewarded ad:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Show a rewarded ad
   */
  async showRewardedAd(): Promise<AdShowResult> {
    try {
      console.log('[Ad Service] Showing rewarded ad...');

      if (this.isWebPlatform()) {
        return await this.showWebRewardedAd();
      } else {
        return await this.showMobileRewardedAd();
      }

    } catch (error) {
      console.error('[Ad Service] Failed to show rewarded ad:', error);
      return {
        success: false,
        rewarded: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Check if rewarded ad is ready to show
   */
  async isRewardedAdReady(): Promise<boolean> {
    if (!this.isInitialized) {
      return false;
    }

    if (this.isWebPlatform()) {
      return this.loadedAds.has('rewarded_web');
    } else {
      // Check mobile ad availability
      return this.loadedAds.has('rewarded_mobile');
    }
  }

  /**
   * Get current ad availability status
   */
  getAdStatus(): { available: boolean; lastError?: string } {
    return {
      available: this.isInitialized && this.loadedAds.size > 0,
      lastError: undefined // Could track last error here
    };
  }

  /**
   * Register callback for ad rewards
   */
  onAdRewarded(callback: (reward: AdReward) => void): void {
    this.rewardCallbacks.push(callback);
  }

  /**
   * Create premium unlock reward
   */
  private createPremiumUnlockReward(): AdReward {
    return {
      type: 'premium_unlock',
      duration: 60 * 60 * 1000, // 1 hour in milliseconds
      features: [
        'binaural_beats',
        'ambient_sounds',
        'games',
        'advanced_sessions',
        'custom_audio'
      ],
      timestamp: Date.now()
    };
  }

  /**
   * Notify reward callbacks
   */
  private notifyRewardCallbacks(reward: AdReward): void {
    this.rewardCallbacks.forEach(callback => {
      try {
        callback(reward);
      } catch (error) {
        console.error('[Ad Service] Reward callback error:', error);
      }
    });
  }

  /**
   * Check if running on web platform
   */
  private isWebPlatform(): boolean {
    return !window.Capacitor || window.Capacitor.getPlatform() === 'web';
  }

  /**
   * Initialize web ads (simulation for development)
   */
  private async initializeWebAds(): Promise<void> {
    console.log('[Ad Service] Initializing web ads (simulation mode)');
    
    // Simulate ad loading delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Mark rewarded ad as loaded
    this.loadedAds.set('rewarded_web', {
      id: 'rewarded_web',
      loaded: true,
      timestamp: Date.now()
    });
  }

  /**
   * Initialize mobile ads using Capacitor AdMob
   */
  private async initializeMobileAds(): Promise<void> {
    console.log('[Ad Service] Initializing mobile ads...');
    
    // This would use the actual Capacitor AdMob plugin
    // const { AdMob } = await import('@capacitor-community/admob');
    
    // await AdMob.initialize({
    //   requestTrackingAuthorization: true,
    //   testingDevices: this.config.testMode ? ['YOUR_TEST_DEVICE_ID'] : [],
    //   initializeForTesting: this.config.testMode
    // });

    // For now, simulate mobile ad initialization
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    this.loadedAds.set('rewarded_mobile', {
      id: 'rewarded_mobile',
      loaded: true,
      timestamp: Date.now()
    });
  }

  /**
   * Load web rewarded ad
   */
  private async loadWebRewardedAd(): Promise<AdLoadResult> {
    // Simulate ad loading
    await new Promise(resolve => setTimeout(resolve, 800));
    
    const adId = `web_rewarded_${Date.now()}`;
    this.loadedAds.set('rewarded_web', {
      id: adId,
      loaded: true,
      timestamp: Date.now()
    });

    return {
      success: true,
      adId
    };
  }

  /**
   * Load mobile rewarded ad
   */
  private async loadMobileRewardedAd(): Promise<AdLoadResult> {
    // This would use the actual Capacitor AdMob plugin
    // const { AdMob, RewardedAdPluginEvents } = await import('@capacitor-community/admob');
    
    // const options = {
    //   adId: this.config.rewardedAdUnitId,
    //   isTesting: this.config.testMode
    // };

    // await AdMob.prepareRewardedVideoAd(options);

    // For now, simulate mobile ad loading
    await new Promise(resolve => setTimeout(resolve, 1200));
    
    const adId = `mobile_rewarded_${Date.now()}`;
    this.loadedAds.set('rewarded_mobile', {
      id: adId,
      loaded: true,
      timestamp: Date.now()
    });

    return {
      success: true,
      adId
    };
  }

  /**
   * Show web rewarded ad
   */
  private async showWebRewardedAd(): Promise<AdShowResult> {
    const ad = this.loadedAds.get('rewarded_web');
    
    if (!ad) {
      return {
        success: false,
        rewarded: false,
        error: 'No ad loaded'
      };
    }

    // Simulate ad display and user interaction
    const userWatchedAd = await this.simulateAdDisplay();
    
    if (userWatchedAd) {
      const reward = this.createPremiumUnlockReward();
      this.notifyRewardCallbacks(reward);
      
      // Remove the used ad
      this.loadedAds.delete('rewarded_web');
      
      return {
        success: true,
        rewarded: true,
        reward
      };
    } else {
      return {
        success: true,
        rewarded: false,
        error: 'User closed ad before completion'
      };
    }
  }

  /**
   * Show mobile rewarded ad
   */
  private async showMobileRewardedAd(): Promise<AdShowResult> {
    const ad = this.loadedAds.get('rewarded_mobile');
    
    if (!ad) {
      return {
        success: false,
        rewarded: false,
        error: 'No ad loaded'
      };
    }

    // This would use the actual Capacitor AdMob plugin
    // const { AdMob } = await import('@capacitor-community/admob');
    // const result = await AdMob.showRewardedVideoAd();

    // For now, simulate mobile ad display
    const userWatchedAd = await this.simulateAdDisplay();
    
    if (userWatchedAd) {
      const reward = this.createPremiumUnlockReward();
      this.notifyRewardCallbacks(reward);
      
      // Remove the used ad
      this.loadedAds.delete('rewarded_mobile');
      
      return {
        success: true,
        rewarded: true,
        reward
      };
    } else {
      return {
        success: true,
        rewarded: false,
        error: 'User closed ad before completion'
      };
    }
  }

  /**
   * Simulate ad display for development/testing
   */
  private async simulateAdDisplay(): Promise<boolean> {
    return new Promise((resolve) => {
      // Show a simple confirmation dialog to simulate ad watching
      const watchAd = window.confirm(
        'Watch a 30-second ad to unlock premium features for 1 hour?\n\n' +
        '(This is a simulation - in production, a real ad would be shown)'
      );
      
      if (watchAd) {
        // Simulate ad duration
        setTimeout(() => {
          alert('Ad completed! Premium features unlocked for 1 hour.');
          resolve(true);
        }, 2000); // Simulate 2 seconds instead of 30 for testing
      } else {
        resolve(false);
      }
    });
  }
}

// Export singleton instance
export const adService = new AdService();

export default adService;
