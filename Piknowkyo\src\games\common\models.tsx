// src/games/common/models.ts

import React from 'react';
import { IconType } from 'react-icons';

// --- Interfaces pour la page GamesPage ---
export interface GameInfo {
  id: string; // ID unique du jeu (ex: "zen-tetris", "memory-challenge")
  titleKey: string; // Clé i18next pour le titre du jeu (ex: "games.zenTetris.title")
  descriptionKey: string; // Clé i18next pour la description du jeu
  thumbnailUrl: string; // URL de l'image de la carte du jeu
  component: React.FC<GameProps>; // Le composant React principal du jeu
  maxLevels: number; // Nombre total de niveaux pour ce jeu
  estimatedDurationMinutes?: number; // Durée estimée d'une partie (optionnel)
  tags?: string[]; // Mots-clés pour le jeu (optionnel)
  icon?: IconType; // Icône du jeu (optionnel)
  // ... toute autre métadonnée utile pour l'affichage ou la logique globale
}

// --- Interfaces pour les props passées à un GameComponent ---
export interface GameProps {
  userId: string; // ID de l'utilisateur connecté pour la sauvegarde
  onGameEnd: (score: number, levelReached: number, finalTimeSeconds: number) => void;
  onGameQuit: () => void; // Callback appelé quand l'utilisateur quitte le jeu (sans le terminer)
  onPauseChange: (isPaused: boolean) => void; // Notifier le conteneur de l'état de pause
  initialGameState?: SavedGameState | null; // <-- CORRECTION ICI : Accepte maintenant 'null'
}

// --- Interfaces pour la sauvegarde de la progression ---
export interface SavedGameState {
  score: number;
  level: number;
  currentTimerSeconds: number; // Temps écoulé dans le niveau actuel ou total
  specificGameState: any; // Données spécifiques au jeu (ex: état de la grille Tetris, cartes retournées)
  lastPlayed: Date; // Date de la dernière sauvegarde
}

// --- Autres interfaces spécifiques aux utilitaires de jeu ---
export interface LeaderboardEntry {
  userId: string;
  gameId: string;
  score: number;
  levelReached: number;
  timestamp: Date;
  // ... d'autres champs pour le classement
}