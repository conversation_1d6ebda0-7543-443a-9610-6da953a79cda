// scripts/populateEmulator.js
const admin = require('firebase-admin');

// Remplace ce chemin par le chemin réel vers ta clé de compte de service JSON.
// EX: si ton script est dans 'scripts' et la clé est dans 'scripts/ma-cle-service.json'
// const serviceAccount = require('./ma-cle-service.json');
const serviceAccount = require('./firebase-adminsdk.json'); // <<< À MODIFIER !

// --- 1. Initialisation du SDK Admin pour ta base de données de PRODUCTION ---
// Donne un nom unique à l'app pour éviter les conflits si tu initialises plusieurs apps
const prodApp = admin.initializeApp({
  credential: admin.credential.cert(serviceAccount),
  // databaseURL: "https://TON_ID_PROJET_PROD.firebaseio.com" // Décommenter si tu utilises Realtime Database
}, 'prodApp'); 

const prodDb = prodApp.firestore();

// --- 2. Initialisation du SDK Admin pour ta base de données EMULATOR LOCALE ---
// Pas besoin de clé de service pour l'émulateur, juste pointer vers localhost.
const emulatorApp = admin.initializeApp({
  projectId: serviceAccount.project_id, // Utilise l'ID de projet de ta clé de service
}, 'emulatorApp'); 

// Configure l'émulateur Firestore
emulatorApp.firestore().settings({
  host: 'localhost:8080', // Port par défaut de l'émulateur Firestore
  ssl: false,             // Pour l'émulateur local, le SSL est désactivé
  ignoreUndefinedProperties: true // Permet d'envoyer des objets avec des propriétés undefined sans erreur
});

const emulatorDb = emulatorApp.firestore();

// --- Fonction principale pour copier les données ---
async function populateEmulator() {
  console.log('Démarrage de la copie des données de production vers l\'émulateur...');

  // --- COPIE : config/pricing ---
  try {
    console.log('Récupération de config/pricing depuis la production...');
    const prodPricingDoc = await prodDb.collection('config').doc('pricing').get();

    if (prodPricingDoc.exists) {
      console.log('Document config/pricing trouvé. Copie vers l\'émulateur...');
      await emulatorDb.collection('config').doc('pricing').set(prodPricingDoc.data());
      console.log('config/pricing copié avec succès !');
    } else {
      console.warn('Document config/pricing non trouvé en production. Ignoré.');
    }
  } catch (error) {
    console.error('Erreur lors de la copie de config/pricing :', error);
  }

  // --- COPIE : Sessions (Exemple : Copier les 10 premières sessions pour ne pas surcharger) ---
  // ATTENTION : Pour un grand nombre de sessions (des milliers/millions), cette méthode sera LENTE et PEUT CONSOMMER BEAUCOUP DE MÉMOIRE.
  // Pour des backups complets et des transferts massifs, la méthode `gcloud firestore export/import` est meilleure.
  try {
    console.log('Récupération de manifests depuis la production...');
    const prodSessionsSnapshot = await prodDb.collection('manifests').limit(300).get(); 

    if (!prodSessionsSnapshot.empty) {
      console.log(`Trouvé ${prodSessionsSnapshot.docs.length} manifests. Copie vers l'émulateur...`);
      for (const doc of prodSessionsSnapshot.docs) {
        await emulatorDb.collection('manifests').doc(doc.id).set(doc.data());
      }
      console.log('manifests copiées avec succès !');
    } else {
      console.warn('Aucun manifests trouvée en production. Ignoré.');
    }
  } catch (error) {
    console.error('Erreur lors de la copie des manifests :', error);
  }

  try {
    console.log('Récupération de quelques scripts depuis la production ...');
    const prodSessionsSnapshot = await prodDb.collection('scripts').limit(300).get(); 

    if (!prodSessionsSnapshot.empty) {
      console.log(`Trouvé ${prodSessionsSnapshot.docs.length} scripts. Copie vers l'émulateur...`);
      for (const doc of prodSessionsSnapshot.docs) {
        await emulatorDb.collection('scripts').doc(doc.id).set(doc.data());
      }
      console.log('scripts copiées avec succès !');
    } else {
      console.warn('Aucun scripts trouvée en production. Ignoré.');
    }
  } catch (error) {
    console.error('Erreur lors de la copie des scripts :', error);
  }
  // --- COPIE : Documents utilisateur (exemple : si tu veux un utilisateur spécifique) ---
  // Tu peux copier les détails de ton utilisateur de test par son UID
  /*
  try {
    const testUserUid = "TON_UID_DE_TEST_ICI"; // Remplace par l'UID de l'utilisateur que tu veux copier
    console.log(`Récupération du profil utilisateur ${testUserUid} depuis la production...`);
    const prodUserDoc = await prodDb.collection('users').doc(testUserUid).get();

    if (prodUserDoc.exists) {
      console.log('Profil utilisateur trouvé. Copie vers l\'émulateur...');
      await emulatorDb.collection('users').doc(testUserUid).set(prodUserDoc.data());
      console.log('Profil utilisateur copié avec succès !');
    } else {
      console.warn(`Profil utilisateur ${testUserUid} non trouvé en production. Ignoré.`);
    }
  } catch (error) {
    console.error('Erreur lors de la copie du profil utilisateur :', error);
  }
  */

  console.log('Copie des données terminée.');
  process.exit(0); // Quitte le script
}

// Lance la fonction de copie
populateEmulator().catch(console.error);