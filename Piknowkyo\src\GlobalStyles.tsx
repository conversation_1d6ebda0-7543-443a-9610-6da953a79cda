import { createGlobalStyle } from 'styled-components';

// Les polices (Montserrat, Roboto, Poppins) doivent être chargées via la balise <link>
// dans votre public/index.html, comme expliqué précédemment.
// Assurez-vous que les noms de police ici correspondent à ceux chargés.

const GlobalStyles = createGlobalStyle`
  /* Reset et base */
  * {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
  }

  html {
    font-size: 16px; // Base pour les unités rem
    scroll-behavior: smooth;
  }

  body {
    margin: 0;
    font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
      'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
      sans-serif; // Poppins en premier, puis fallbacks système
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    background: ${({ theme }) => theme.background};
    color: ${({ theme }) => theme.text};
    line-height: 1.6;
    transition: background-color 0.3s ease, color 0.3s ease; // Transition pour le changement de thème
  }

  /* Typographie de base */
  h1, h2, h3, h4, h5, h6 {
    font-family: 'Montserrat', sans-serif; // Montserrat pour les titres
    font-weight: 600; // Un poids commun pour les titres
    color: ${({ theme }) => theme.primary}; // Couleur primaire pour les titres
    margin-top: 1.5rem; // Espacement avant les titres
    margin-bottom: 1rem; // Espacement après les titres
    line-height: 1.3; // Hauteur de ligne pour une meilleure lisibilité
  }

  h1 {
    font-size: 2.2rem; // Ajusté pour cohérence
  }

  h2 {
    font-size: 1.8rem; // Ajusté
  }

  h3 {
    font-size: 1.5rem;
  }

  p {
    margin-bottom: 1rem;
    font-family: 'Roboto', sans-serif; // Roboto pour le corps du texte si Poppins n'est pas disponible
    /* Si Poppins est la police principale du corps, vous pouvez enlever cette ligne
       ou la mettre en fallback après Poppins dans la déclaration body.font-family */
  }

  a {
    color: ${({ theme }) => theme.accent || theme.primary}; // Utiliser accent, sinon primary pour les liens
    text-decoration: none;
    transition: color 0.2s ease;

    &:hover {
      color: ${({ theme }) => theme.primary};
      text-decoration: underline; // Souligner au survol pour une meilleure affordance
    }
  }

  button {
    cursor: pointer;
    font-family: 'Poppins', sans-serif; // Poppins pour les boutons
    border: none;
    background: ${({ theme }) => theme.primary};
    color: ${({ theme }) => theme.textLight || 'white'}; // Assurer un fallback pour textLight
    padding: 0.6rem 1.2rem; // Padding ajusté
    border-radius: 8px; // Arrondi plus doux
    font-weight: 500;
    transition: all 0.2s ease;
    display: inline-flex; // Pour un meilleur alignement avec les icônes si utilisées
    align-items: center;
    justify-content: center;

    &:hover:not(:disabled) { // Ne pas appliquer l'effet de survol si désactivé
      opacity: 0.85;
      transform: translateY(-1px); // Effet de survol subtil
    }

    &:active:not(:disabled) {
      transform: translateY(0px); // Effet de clic
      opacity: 1;
    }

    &:disabled {
      background: ${({ theme }) => theme.disabledBackground || '#cccccc'};
      color: ${({ theme }) => theme.disabledText || '#666666'};
      cursor: not-allowed;
      transform: none;
      opacity: 0.7;
    }
  }

  /* Améliorations pour les éléments d'interface existants (si vous les utilisez globalement) */
  /* Ces styles sont très spécifiques et pourraient être mieux placés dans les composants concernés
     si .app-container, .app-header etc. ne sont pas des classes globales mais des styled-components.
     Pour l'instant, je les garde ici en supposant que vous les utilisez comme classes globales. */

  .app-container {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
  }

  .app-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 2rem;
    background: ${({ theme }) => theme.surface};
    border-bottom: 1px solid ${({ theme }) => theme.border};
    box-shadow: ${({ theme }) => theme.headerShadow || '0 2px 4px rgba(0,0,0,0.03)'};
    position: sticky;
    top: 0;
    z-index: 100; // Assurez un z-index suffisamment élevé

    @media (max-width: 768px) {
      padding: 0.75rem 1rem;
    }
  }

  .logo {
    display: flex;
    align-items: center;
    font-weight: 700; // Plus gras pour le logo
    font-size: 1.5rem;
    color: ${({ theme }) => theme.primary};
    font-family: 'Montserrat', sans-serif; // Montserrat pour le logo

    img { // Styles pour l'image dans le logo
      height: 40px; // Taille d'image fixe
      margin-right: 0.75rem;
      transition: transform 0.3s ease;
    }
    
    &:hover img {
      transform: scale(1.05);
    }

    @media (max-width: 768px) {
      font-size: 1.25rem;
      img {
        height: 32px;
        margin-right: 0.5rem;
      }
    }
  }

  .header-actions {
    display: flex;
    align-items: center;
    gap: 0.75rem; // Un peu plus d'espace
  }

  .theme-toggle, .menu-toggle { // Styles pour les boutons d'icône
    background: transparent; // Fond transparent
    border: none;
    color: ${({ theme }) => theme.text};
    font-size: 1.3rem; // Taille d'icône
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s ease, color 0.2s ease;

    &:hover {
      background: ${({ theme }) => theme.surfaceAlt || `${theme.primary}1A`}; // Couleur de survol subtile
      color: ${({ theme }) => theme.primary};
      transform: none; // Enlever le transform des boutons généraux ici
    }
  }

  .app-main {
    flex: 1; /* Permet au contenu principal de prendre l'espace restant */
    padding: 1.5rem; // Padding standard
    max-width: 1200px; // Conteneur max pour le contenu
    margin: 0 auto; // Centrer le conteneur
    width: 100%;
    padding-bottom: 4rem; // Espace pour le footer ou la fin de page

    @media (max-width: 768px) {
      padding: 1rem; // Moins de padding sur mobile
    }
  }

  /* Styles responsives pour la typographie */
  @media (max-width: 768px) {
    h1 {
      font-size: 1.8rem; // Taille ajustée pour mobile
    }
    h2 {
      font-size: 1.6rem;
    }
    h3 {
      font-size: 1.35rem;
    }
    p {
      font-size: 0.95rem;
    }
  }

  /* Cartes et conteneurs génériques */
  .card, div[class*="Card"] { // Cible .card et les classes contenant "Card"
    background: ${({ theme }) => theme.surface};
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1.5rem; // Espacement standard pour les cartes
    box-shadow: ${({ theme }) => theme.cardShadow || '0 4px 12px rgba(0,0,0,0.07)'};
    transition: box-shadow 0.3s ease;

    &:hover {
      box-shadow: ${({ theme }) => theme.cardHoverShadow || '0 6px 16px rgba(0,0,0,0.1)'};
    }
  }

  /* Styles pour les formulaires */
  input[type="text"],
  input[type="email"],
  input[type="password"],
  input[type="number"],
  input[type="search"],
  input[type="tel"],
  input[type="url"],
  select,
  textarea {
    width: 100%;
    padding: 0.75rem 1rem; // Padding plus généreux
    margin-bottom: 1rem; // Espacement standard sous les inputs
    border: 1px solid ${({ theme }) => theme.border};
    border-radius: 8px; // Arrondi plus doux
    font-family: 'Poppins', sans-serif;
    font-size: 1rem; // Taille de police standard pour les inputs
    background-color: ${({ theme }) => theme.inputBackground};
    color: ${({ theme }) => theme.text};
    transition: border-color 0.2s ease, box-shadow 0.2s ease;

    &:focus {
      outline: none;
      border-color: ${({ theme }) => theme.primary};
      box-shadow: 0 0 0 3px ${({ theme }) => `${theme.primary}33`}; // Ombre de focus plus visible
    }

    &::placeholder {
      color: ${({ theme }) => theme.textMuted};
      opacity: 0.7;
    }
  }

  /* Animations */
  @keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); } // Ajout d'un léger effet de translation
    to { opacity: 1; transform: translateY(0); }
  }

  .fade-in {
    animation: fadeIn 0.5s ease-in-out forwards; // 'forwards' pour maintenir l'état final
  }

  /* Scrollbar personnalisée (optionnel, mais souvent souhaité) */
  ::-webkit-scrollbar {
    width: 10px; // Un peu plus large
    height: 10px;
  }

  ::-webkit-scrollbar-track {
    background: ${({ theme }) => theme.surfaceAlt || theme.background}; // Fond de la piste
    border-radius: 10px;
  }

  ::-webkit-scrollbar-thumb {
    background: ${({ theme }) => theme.primary};
    border-radius: 10px;
    border: 2px solid ${({ theme }) => theme.surfaceAlt || theme.background}; // Crée un effet de bordure

    &:hover {
      background: ${({ theme }) => theme.accent || `darken(${theme.primary}, 10%)`}; // Assombrir au survol
    }
  }
`;

export default GlobalStyles;