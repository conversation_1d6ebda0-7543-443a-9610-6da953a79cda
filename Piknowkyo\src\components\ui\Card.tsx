import React from 'react';
import styled from 'styled-components';

interface CardProps {
  children: React.ReactNode;
  elevated?: boolean;
  gradient?: boolean;
  noPadding?: boolean;
  onClick?: () => void;
  className?: string;
}

const StyledCard = styled.div<{
  $elevated?: boolean;
  $gradient?: boolean;
  $noPadding?: boolean;
}>`
  background: ${({ theme, $gradient }) => 
    $gradient 
      ? `linear-gradient(135deg, ${theme.primary}, ${theme.accent || '#17a2b8'})` 
      : theme.surface
  };
  color: ${({ theme, $gradient }) => $gradient ? '#ffffff' : theme.text};
  border-radius: 12px;
  box-shadow: ${({ $elevated }) => 
    $elevated 
      ? '0 8px 16px rgba(0, 0, 0, 0.1)' 
      : '0 4px 6px rgba(0, 0, 0, 0.05)'
  };
  margin: 1rem 0;
  padding: ${({ $noPadding }) => $noPadding ? '0' : '1.5rem'};
  transition: all 0.3s ease;
  
  &:hover {
    box-shadow: ${({ $elevated }) => 
      $elevated 
        ? '0 12px 20px rgba(0, 0, 0, 0.15)' 
        : '0 6px 12px rgba(0, 0, 0, 0.08)'
    };
    transform: ${({ $elevated }) => $elevated ? 'translateY(-5px)' : 'none'};
  }
`;

const Card: React.FC<CardProps> = ({ 
  children, 
  elevated = false, 
  gradient = false, 
  noPadding = false,
  onClick,
  className
}) => {
  return (
    <StyledCard 
      $elevated={elevated} 
      $gradient={gradient} 
      $noPadding={noPadding}
      onClick={onClick}
      className={className}
    >
      {children}
    </StyledCard>
  );
};

export default Card;
