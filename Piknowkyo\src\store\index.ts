import { configureStore, combineReducers } from '@reduxjs/toolkit';
import { persistStore, persistReducer, FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER } from 'redux-persist';
import storage from 'redux-persist/lib/storage';

// Import des slices
import authSlice from './slices/authSlice';
import sessionsSlice from './slices/sessionsSlice';
import journalSlice from './slices/journalSlice';
import audioAssetsSlice from './slices/audioAssetsSlice';
import syncSlice from './slices/syncSlice';
import networkSlice from './slices/networkSlice';
import subscriptionSlice from './slices/subscriptionSlice';

// Configuration de la persistance
const persistConfig = {
  key: 'piknowkyo-root',
  version: 1,
  storage,
  whitelist: ['auth', 'sessions', 'journal', 'audioAssets', 'sync', 'subscription'], // Ne pas persister network
};

// Configuration spécifique pour les données sensibles
const authPersistConfig = {
  key: 'auth',
  storage,
};

// Combinaison des reducers
const rootReducer = combineReducers({
  auth: persistReducer(authPersistConfig, authSlice),
  sessions: sessionsSlice,
  journal: journalSlice,
  audioAssets: audioAssetsSlice,
  sync: syncSlice,
  network: networkSlice,
  subscription: subscriptionSlice,
});

const persistedReducer = persistReducer(persistConfig, rootReducer);

// Configuration du store
const store = configureStore({
  reducer: persistedReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: [FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER],
      },
    }),
  devTools: process.env.NODE_ENV !== 'production',
});

const persistor = persistStore(store);

// Types pour TypeScript
type RootState = ReturnType<typeof store.getState>;
type AppDispatch = typeof store.dispatch;

export { store, persistor };
export type { RootState, AppDispatch };
