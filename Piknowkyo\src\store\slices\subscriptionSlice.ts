import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { AdReward } from '../../services/adService';

export interface SubscriptionState {
  // Subscription status
  isActive: boolean;
  tier: 'free' | 'premium';
  subscriptionId: string | null;
  
  // Trial information
  isTrialActive: boolean;
  trialStartDate: string | null;
  trialEndDate: string | null;
  trialDaysRemaining: number;
  
  // Payment information
  currentPeriodStart: string | null;
  currentPeriodEnd: string | null;
  cancelAtPeriodEnd: boolean;
  
  // Ad-based unlocks
  adUnlocks: {
    isActive: boolean;
    unlockedUntil: string | null;
    features: string[];
    adsWatchedToday: number;
    lastAdWatchDate: string | null;
  };
  
  // Premium features access
  premiumFeatures: {
    games: boolean;
    binauralBeats: boolean;
    ambientSounds: boolean;
    advancedSessions: boolean;
    customAudio: boolean;
    noAds: boolean;
    cloudTTS: boolean;
    advancedAnalytics: boolean;
  };
  
  // Loading states
  isLoading: boolean;
  isUpdating: boolean;
  error: string | null;
  
  // Billing
  nextBillingDate: string | null;
  lastPaymentDate: string | null;
  paymentMethod: string | null;
}

const initialState: SubscriptionState = {
  isActive: false,
  tier: 'free',
  subscriptionId: null,
  
  isTrialActive: false,
  trialStartDate: null,
  trialEndDate: null,
  trialDaysRemaining: 0,
  
  currentPeriodStart: null,
  currentPeriodEnd: null,
  cancelAtPeriodEnd: false,
  
  adUnlocks: {
    isActive: false,
    unlockedUntil: null,
    features: [],
    adsWatchedToday: 0,
    lastAdWatchDate: null,
  },
  
  premiumFeatures: {
    games: false,
    binauralBeats: false,
    ambientSounds: false,
    advancedSessions: false,
    customAudio: false,
    noAds: false,
    cloudTTS: false,
    advancedAnalytics: false,
  },
  
  isLoading: false,
  isUpdating: false,
  error: null,
  
  nextBillingDate: null,
  lastPaymentDate: null,
  paymentMethod: null,
};

// Async thunks
export const fetchSubscriptionStatus = createAsyncThunk(
  'subscription/fetchStatus',
  async (userId: string, { rejectWithValue }) => {
    try {
      // This would fetch from Firebase/Stripe
      // const subscriptionData = await subscriptionService.getSubscription(userId);
      
      // For now, return mock data
      const mockSubscription = {
        isActive: false,
        tier: 'free' as const,
        subscriptionId: null,
        isTrialActive: false,
        trialStartDate: null,
        trialEndDate: null,
        currentPeriodStart: null,
        currentPeriodEnd: null,
        cancelAtPeriodEnd: false,
        nextBillingDate: null,
        lastPaymentDate: null,
        paymentMethod: null,
      };
      
      return mockSubscription;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

export const startFreeTrial = createAsyncThunk(
  'subscription/startTrial',
  async (userId: string, { rejectWithValue }) => {
    try {
      const trialStartDate = new Date().toISOString();
      const trialEndDate = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(); // 7 days
      
      // This would create trial in Firebase/Stripe
      // await subscriptionService.startTrial(userId, trialStartDate, trialEndDate);
      
      return {
        trialStartDate,
        trialEndDate,
        isTrialActive: true,
      };
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

export const createSubscription = createAsyncThunk(
  'subscription/create',
  async ({ userId, priceId }: { userId: string; priceId: string }, { rejectWithValue }) => {
    try {
      // This would create subscription via Stripe
      // const subscription = await subscriptionService.createSubscription(userId, priceId);
      
      const mockSubscription = {
        subscriptionId: `sub_${Date.now()}`,
        isActive: true,
        tier: 'premium' as const,
        currentPeriodStart: new Date().toISOString(),
        currentPeriodEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
        nextBillingDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
        paymentMethod: 'card',
      };
      
      return mockSubscription;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

export const cancelSubscription = createAsyncThunk(
  'subscription/cancel',
  async (subscriptionId: string, { rejectWithValue }) => {
    try {
      // This would cancel subscription via Stripe
      // await subscriptionService.cancelSubscription(subscriptionId);
      
      return {
        cancelAtPeriodEnd: true,
      };
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

const subscriptionSlice = createSlice({
  name: 'subscription',
  initialState,
  reducers: {
    // Ad unlock management
    applyAdReward: (state, action: PayloadAction<AdReward>) => {
      const reward = action.payload;
      const unlockEndTime = new Date(reward.timestamp + reward.duration).toISOString();
      
      state.adUnlocks.isActive = true;
      state.adUnlocks.unlockedUntil = unlockEndTime;
      state.adUnlocks.features = reward.features;
      
      // Update ads watched today
      const today = new Date().toDateString();
      if (state.adUnlocks.lastAdWatchDate !== today) {
        state.adUnlocks.adsWatchedToday = 1;
        state.adUnlocks.lastAdWatchDate = today;
      } else {
        state.adUnlocks.adsWatchedToday += 1;
      }
      
      // Update premium features based on ad unlock
      state.premiumFeatures.games = true;
      state.premiumFeatures.binauralBeats = true;
      state.premiumFeatures.ambientSounds = true;
      state.premiumFeatures.advancedSessions = true;
      state.premiumFeatures.customAudio = true;
    },
    
    // Check and update ad unlock status
    checkAdUnlockExpiry: (state) => {
      if (state.adUnlocks.isActive && state.adUnlocks.unlockedUntil) {
        const now = new Date();
        const unlockEnd = new Date(state.adUnlocks.unlockedUntil);
        
        if (now >= unlockEnd) {
          state.adUnlocks.isActive = false;
          state.adUnlocks.unlockedUntil = null;
          state.adUnlocks.features = [];
          
          // Reset premium features if no active subscription
          if (!state.isActive && !state.isTrialActive) {
            state.premiumFeatures.games = false;
            state.premiumFeatures.binauralBeats = false;
            state.premiumFeatures.ambientSounds = false;
            state.premiumFeatures.advancedSessions = false;
            state.premiumFeatures.customAudio = false;
          }
        }
      }
    },
    
    // Update trial days remaining
    updateTrialDaysRemaining: (state) => {
      if (state.isTrialActive && state.trialEndDate) {
        const now = new Date();
        const trialEnd = new Date(state.trialEndDate);
        const daysRemaining = Math.ceil((trialEnd.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
        
        state.trialDaysRemaining = Math.max(0, daysRemaining);
        
        // Check if trial has expired
        if (daysRemaining <= 0) {
          state.isTrialActive = false;
          state.trialDaysRemaining = 0;
          
          // Reset premium features if no active subscription
          if (!state.isActive) {
            Object.keys(state.premiumFeatures).forEach(key => {
              (state.premiumFeatures as any)[key] = false;
            });
          }
        }
      }
    },
    
    // Update premium features based on subscription status
    updatePremiumFeatures: (state) => {
      const hasAccess = state.isActive || state.isTrialActive || state.adUnlocks.isActive;
      
      if (hasAccess) {
        state.premiumFeatures.games = true;
        state.premiumFeatures.binauralBeats = true;
        state.premiumFeatures.ambientSounds = true;
        state.premiumFeatures.advancedSessions = true;
        state.premiumFeatures.customAudio = true;
        state.premiumFeatures.advancedAnalytics = true;
        
        // Only full subscription gets these features
        if (state.isActive || state.isTrialActive) {
          state.premiumFeatures.noAds = true;
          state.premiumFeatures.cloudTTS = true;
        }
      } else {
        Object.keys(state.premiumFeatures).forEach(key => {
          (state.premiumFeatures as any)[key] = false;
        });
      }
    },
    
    // Clear subscription data
    clearSubscription: (state) => {
      return { ...initialState };
    },
    
    // Clear errors
    clearError: (state) => {
      state.error = null;
    },
  },
  
  extraReducers: (builder) => {
    // Fetch subscription status
    builder
      .addCase(fetchSubscriptionStatus.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchSubscriptionStatus.fulfilled, (state, action) => {
        state.isLoading = false;
        Object.assign(state, action.payload);
        subscriptionSlice.caseReducers.updatePremiumFeatures(state);
      })
      .addCase(fetchSubscriptionStatus.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
    
    // Start free trial
    builder
      .addCase(startFreeTrial.pending, (state) => {
        state.isUpdating = true;
        state.error = null;
      })
      .addCase(startFreeTrial.fulfilled, (state, action) => {
        state.isUpdating = false;
        state.isTrialActive = action.payload.isTrialActive;
        state.trialStartDate = action.payload.trialStartDate;
        state.trialEndDate = action.payload.trialEndDate;
        state.trialDaysRemaining = 7;
        subscriptionSlice.caseReducers.updatePremiumFeatures(state);
      })
      .addCase(startFreeTrial.rejected, (state, action) => {
        state.isUpdating = false;
        state.error = action.payload as string;
      });
    
    // Create subscription
    builder
      .addCase(createSubscription.pending, (state) => {
        state.isUpdating = true;
        state.error = null;
      })
      .addCase(createSubscription.fulfilled, (state, action) => {
        state.isUpdating = false;
        state.isActive = true;
        state.tier = 'premium';
        state.subscriptionId = action.payload.subscriptionId;
        state.currentPeriodStart = action.payload.currentPeriodStart;
        state.currentPeriodEnd = action.payload.currentPeriodEnd;
        state.nextBillingDate = action.payload.nextBillingDate;
        state.paymentMethod = action.payload.paymentMethod;
        
        // End trial if active
        state.isTrialActive = false;
        state.trialDaysRemaining = 0;
        
        subscriptionSlice.caseReducers.updatePremiumFeatures(state);
      })
      .addCase(createSubscription.rejected, (state, action) => {
        state.isUpdating = false;
        state.error = action.payload as string;
      });
    
    // Cancel subscription
    builder
      .addCase(cancelSubscription.pending, (state) => {
        state.isUpdating = true;
        state.error = null;
      })
      .addCase(cancelSubscription.fulfilled, (state, action) => {
        state.isUpdating = false;
        state.cancelAtPeriodEnd = action.payload.cancelAtPeriodEnd;
      })
      .addCase(cancelSubscription.rejected, (state, action) => {
        state.isUpdating = false;
        state.error = action.payload as string;
      });
  },
});

export const {
  applyAdReward,
  checkAdUnlockExpiry,
  updateTrialDaysRemaining,
  updatePremiumFeatures,
  clearSubscription,
  clearError,
} = subscriptionSlice.actions;

export default subscriptionSlice.reducer;
