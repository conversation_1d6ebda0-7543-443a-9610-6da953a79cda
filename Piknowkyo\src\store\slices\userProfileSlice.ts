// src/store/slices/userProfileSlice.ts (VERSION ADAPTÉE À VOTRE TTSProvider)
import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { doc, getDoc, setDoc } from 'firebase/firestore';
import { db } from '../../firebase';

// Importez le type TTSProvider depuis votre fichier tts.ts
import { TTSProvider } from '../../services/tts'; // <-- NOUVEL IMPORT ICI

// --- INTERFACE CORRIGÉE : Pour les préférences audio de l'utilisateur ---
export interface UserAudioPreferences {
  enableMusic?: boolean;
  music?: {
    volume?: number;
  };
  enableAmbient?: boolean;
  ambient?: {
    volume?: number;
  };
  enableBinaural?: boolean;
  binaural?: {
    volume?: number;
    baseFreq?: number;
    beatFreq?: number;
  };
  voice?: {
    volume?: number;
    provider?: TTSProvider; // <-- UTILISE LE TYPE TTSProvider DE VOTRE TTS.TS
    voice?: string; // Nom/ID de la voix spécifique
  };
}

export interface UserProfileData {
  uid: string;
  email: string;
  displayName: string | null;
  createdAt?: Date;
  lastLoginAt?: Date;
  preferences: {
    theme: 'light' | 'dark';
    language: 'en' | 'fr';
    subscriptions: {
      active: boolean;
      tier: 'free' | 'premium';
      startDate: string | null;
      endsAt: string | null;
    };
    adWatchData: {
      lastAdWatchDay: string | null;
      adsWatchedToday: number;
      adFreeUntil: string | null;
    };
    stats: {
      totalSessionsCompleted: 0;
      totalMinutesMeditated: 0;
      longestStreak: 0;
      currentStreak: 0;
      lastSessionPlayedAt: null;
    };
    audioConfig?: UserAudioPreferences;
  };
  // Ajoutez d'autres champs si nécessaire
}

interface UserProfileState {
  profile: UserProfileData | null;
  loading: boolean;
  saving: boolean;
  error: string | null;
}

const initialState: UserProfileState = {
  profile: null,
  loading: false,
  saving: false,
  error: null,
};

const convertTimestampsToDates = (data: any): any => {
  if (data.createdAt && typeof data.createdAt.toDate === 'function') {
    data.createdAt = data.createdAt.toDate();
  }
  if (data.lastLoginAt && typeof data.lastLoginAt.toDate === 'function') {
    data.lastLoginAt = data.lastLoginAt.toDate();
  }
  if (data.preferences?.subscriptions?.startDate && typeof data.preferences.subscriptions.startDate.toDate === 'function') {
    data.preferences.subscriptions.startDate = data.preferences.subscriptions.startDate.toDate().toISOString();
  }
  if (data.preferences?.subscriptions?.endsAt && typeof data.preferences.subscriptions.endsAt.toDate === 'function') {
    data.preferences.subscriptions.endsAt = data.preferences.subscriptions.endsAt.toDate().toISOString();
  }
  if (data.preferences?.stats?.lastSessionPlayedAt && typeof data.preferences.stats.lastSessionPlayedAt.toDate === 'function') {
    data.preferences.stats.lastSessionPlayedAt = data.preferences.stats.lastSessionPlayedAt.toDate().toISOString();
  }
  return data;
};


export const fetchUserProfile = createAsyncThunk(
  'userProfile/fetchUserProfile',
  async (uid: string, { rejectWithValue }) => {
    try {
      const userDocRef = doc(db, 'users', uid);
      const docSnap = await getDoc(userDocRef);

      if (docSnap.exists()) {
        const profileData = convertTimestampsToDates(docSnap.data() as UserProfileData);
        return { uid: docSnap.id, ...profileData };
      } else {
        return rejectWithValue('Profil utilisateur non trouvé.');
      }
    } catch (error: any) {
      console.error("Error fetching user profile:", error);
      return rejectWithValue(error.message || 'Échec du chargement du profil utilisateur.');
    }
  }
);

export const createUserProfile = createAsyncThunk(
  'userProfile/createUserProfile',
  async (initialData: { uid: string; email: string; displayName: string | null; }, { rejectWithValue }) => {
    try {
      const newUserProfile: UserProfileData = {
        uid: initialData.uid,
        email: initialData.email,
        displayName: initialData.displayName,
        createdAt: new Date(),
        lastLoginAt: new Date(),
        preferences: {
          theme: 'light', // Thème par défaut
          language: 'fr', // Langue par défaut
          subscriptions: {
            active: false,
            tier: 'free',
            startDate: null,
            endsAt: null,
          },
          adWatchData: {
            lastAdWatchDay: null,
            adsWatchedToday: 0,
            adFreeUntil: null,
          },
          stats: {
            totalSessionsCompleted: 0,
            totalMinutesMeditated: 0,
            longestStreak: 0,
            currentStreak: 0,
            lastSessionPlayedAt: null,
          },
          audioConfig: { // Initialisation des préférences audio par défaut
            enableMusic: false,
            music: { volume: 0.5 },
            enableAmbient: false,
            ambient: { volume: 0.3 },
            enableBinaural: false,
            binaural: { volume: 0.2, baseFreq: 100, beatFreq: 10 },
            voice: { volume: 1, provider: 'browser', voice: 'auto' }, // <-- 'browser' est votre provider par défaut compatible
          },
        },
      };

      const userDocRef = doc(db, 'users', initialData.uid);
      await setDoc(userDocRef, newUserProfile);

      return newUserProfile;
    } catch (error: any) {
      console.error("Error creating user profile:", error);
      return rejectWithValue(error.message || 'Échec de la création du profil utilisateur.');
    }
  }
);

export const updateUserProfile = createAsyncThunk(
  'userProfile/updateUserProfile',
  async (profileData: UserProfileData, { rejectWithValue }) => {
    try {
      const userDocRef = doc(db, 'users', profileData.uid);
      await setDoc(userDocRef, profileData, { merge: true });

      return profileData;
    } catch (error: any) {
      console.error("Error updating user profile:", error);
      return rejectWithValue(error.message || 'Échec de la mise à jour du profil utilisateur.');
    }
  }
);

const userProfileSlice = createSlice({
  name: 'userProfile',
  initialState,
  reducers: {
    clearUserProfile: (state) => {
      state.profile = null;
      state.error = null;
      state.loading = false;
      state.saving = false;
    },
  },
  extraReducers: (builder) => {
    builder
      // fetchUserProfile
      .addCase(fetchUserProfile.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchUserProfile.fulfilled, (state, action: PayloadAction<UserProfileData>) => {
        state.loading = false;
        state.profile = action.payload;
      })
      .addCase(fetchUserProfile.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // createUserProfile
      .addCase(createUserProfile.pending, (state) => {
        state.saving = true;
        state.error = null;
      })
      .addCase(createUserProfile.fulfilled, (state, action: PayloadAction<UserProfileData>) => {
        state.saving = false;
        state.profile = action.payload;
        state.error = null;
      })
      .addCase(createUserProfile.rejected, (state, action) => {
        state.saving = false;
        state.error = action.payload as string;
      })
      // updateUserProfile
      .addCase(updateUserProfile.pending, (state) => {
        state.saving = true;
        state.error = null;
      })
      .addCase(updateUserProfile.fulfilled, (state, action: PayloadAction<UserProfileData>) => {
        state.saving = false;
        state.profile = action.payload;
      })
      .addCase(updateUserProfile.rejected, (state, action) => {
        state.saving = false;
        state.error = action.payload as string;
      });
  },
});

export const { clearUserProfile } = userProfileSlice.actions;
export default userProfileSlice.reducer;