import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export interface NetworkState {
  isOnline: boolean;
  connectionType: 'wifi' | 'cellular' | 'ethernet' | 'unknown';
  isSlowConnection: boolean;
  lastOnlineTimestamp: number | null;
  offlineDuration: number;
}

const initialState: NetworkState = {
  isOnline: navigator.onLine,
  connectionType: 'unknown',
  isSlowConnection: false,
  lastOnlineTimestamp: navigator.onLine ? Date.now() : null,
  offlineDuration: 0,
};

const networkSlice = createSlice({
  name: 'network',
  initialState,
  reducers: {
    setOnlineStatus: (state, action: PayloadAction<boolean>) => {
      const wasOffline = !state.isOnline;
      state.isOnline = action.payload;
      
      if (action.payload) {
        // Revenu en ligne
        if (wasOffline && state.lastOnlineTimestamp) {
          state.offlineDuration = Date.now() - state.lastOnlineTimestamp;
        }
        state.lastOnlineTimestamp = Date.now();
      } else {
        // Passé hors ligne
        state.lastOnlineTimestamp = Date.now();
      }
    },
    setConnectionType: (state, action: PayloadAction<'wifi' | 'cellular' | 'ethernet' | 'unknown'>) => {
      state.connectionType = action.payload;
    },
    setSlowConnection: (state, action: PayloadAction<boolean>) => {
      state.isSlowConnection = action.payload;
    },
    updateOfflineDuration: (state) => {
      if (!state.isOnline && state.lastOnlineTimestamp) {
        state.offlineDuration = Date.now() - state.lastOnlineTimestamp;
      }
    },
    resetNetworkState: (state) => {
      state.isOnline = navigator.onLine;
      state.connectionType = 'unknown';
      state.isSlowConnection = false;
      state.lastOnlineTimestamp = navigator.onLine ? Date.now() : null;
      state.offlineDuration = 0;
    },
  },
});

export const {
  setOnlineStatus,
  setConnectionType,
  setSlowConnection,
  updateOfflineDuration,
  resetNetworkState,
} = networkSlice.actions;

export default networkSlice.reducer;
