import React, { useState } from 'react';
import styled from 'styled-components';
import { useTranslation } from 'react-i18next';
import { FiLock, FiPlay, FiStar, FiGift, FiCrown } from 'react-icons/fi';
import { useAppSelector } from '../store/hooks';
import { adService } from '../services/adService';
import Button from './ui/Button';

const GateContainer = styled.div`
  position: relative;
  border-radius: 12px;
  overflow: hidden;
  background: ${({ theme }) => theme.surface};
  border: 2px solid ${({ theme }) => theme.border};
`;

const LockedOverlay = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(4px);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 10;
  padding: 2rem;
  text-align: center;
`;

const LockIcon = styled.div`
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: ${({ theme }) => theme.primary};
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
  color: white;
`;

const Title = styled.h3`
  color: white;
  margin: 0 0 0.5rem 0;
  font-size: 1.25rem;
  font-weight: 600;
`;

const Description = styled.p`
  color: rgba(255, 255, 255, 0.8);
  margin: 0 0 1.5rem 0;
  font-size: 0.875rem;
  line-height: 1.4;
  max-width: 300px;
`;

const UnlockOptions = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  width: 100%;
  max-width: 280px;
`;

const TrialBanner = styled.div`
  background: linear-gradient(135deg, #ffd700, #ffed4e);
  color: #000;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 600;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
`;

const AdUnlockBanner = styled.div`
  background: linear-gradient(135deg, #4ade80, #22c55e);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 600;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
`;

const ContentWrapper = styled.div<{ $blurred: boolean }>`
  filter: ${({ $blurred }) => $blurred ? 'blur(2px)' : 'none'};
  pointer-events: ${({ $blurred }) => $blurred ? 'none' : 'auto'};
  transition: filter 0.3s ease;
`;

interface PremiumGateProps {
  feature: 'games' | 'binauralBeats' | 'ambientSounds' | 'advancedSessions' | 'customAudio' | 'cloudTTS' | 'advancedAnalytics';
  children: React.ReactNode;
  title?: string;
  description?: string;
  showAdOption?: boolean;
  className?: string;
}

const PremiumGate: React.FC<PremiumGateProps> = ({
  feature,
  children,
  title,
  description,
  showAdOption = true,
  className
}) => {
  const { t } = useTranslation();
  const [isLoadingAd, setIsLoadingAd] = useState(false);
  
  const subscription = useAppSelector(state => state.subscription);
  const hasFeatureAccess = subscription.premiumFeatures[feature];
  
  // If user has access, render children directly
  if (hasFeatureAccess) {
    return <div className={className}>{children}</div>;
  }

  const getFeatureTitle = (): string => {
    if (title) return title;
    
    switch (feature) {
      case 'games':
        return t('premium.features.games.title', 'Mindfulness Games');
      case 'binauralBeats':
        return t('premium.features.binaural.title', 'Binaural Beats');
      case 'ambientSounds':
        return t('premium.features.ambient.title', 'Ambient Sounds');
      case 'advancedSessions':
        return t('premium.features.advanced.title', 'Advanced Sessions');
      case 'customAudio':
        return t('premium.features.custom.title', 'Custom Audio');
      case 'cloudTTS':
        return t('premium.features.tts.title', 'Cloud TTS Voices');
      case 'advancedAnalytics':
        return t('premium.features.analytics.title', 'Advanced Analytics');
      default:
        return t('premium.features.default.title', 'Premium Feature');
    }
  };

  const getFeatureDescription = (): string => {
    if (description) return description;
    
    switch (feature) {
      case 'games':
        return t('premium.features.games.description', 'Access mindfulness games and interactive meditation exercises.');
      case 'binauralBeats':
        return t('premium.features.binaural.description', 'Enhance your meditation with scientifically-designed binaural beats.');
      case 'ambientSounds':
        return t('premium.features.ambient.description', 'Create the perfect atmosphere with premium ambient soundscapes.');
      case 'advancedSessions':
        return t('premium.features.advanced.description', 'Unlock advanced meditation and hypnosis sessions.');
      case 'customAudio':
        return t('premium.features.custom.description', 'Upload and use your own audio files in sessions.');
      case 'cloudTTS':
        return t('premium.features.tts.description', 'Access premium cloud-based text-to-speech voices.');
      case 'advancedAnalytics':
        return t('premium.features.analytics.description', 'Get detailed insights into your meditation progress.');
      default:
        return t('premium.features.default.description', 'This is a premium feature available with subscription.');
    }
  };

  const handleWatchAd = async () => {
    setIsLoadingAd(true);
    
    try {
      // Load and show rewarded ad
      const loadResult = await adService.loadRewardedAd();
      
      if (loadResult.success) {
        const showResult = await adService.showRewardedAd();
        
        if (showResult.success && showResult.rewarded) {
          // Ad reward will be handled by the ad service callback
          // which will update the Redux store
        }
      }
    } catch (error) {
      console.error('Failed to show ad:', error);
    } finally {
      setIsLoadingAd(false);
    }
  };

  const handleStartTrial = () => {
    // Navigate to subscription page or show trial modal
    window.location.href = '/monetization?action=trial';
  };

  const handleSubscribe = () => {
    // Navigate to subscription page
    window.location.href = '/monetization?action=subscribe';
  };

  const canStartTrial = !subscription.isTrialActive && 
                       subscription.trialStartDate === null && 
                       !subscription.isActive;

  const hasActiveAdUnlock = subscription.adUnlocks.isActive;
  const adUnlockTimeRemaining = subscription.adUnlocks.unlockedUntil 
    ? Math.max(0, new Date(subscription.adUnlocks.unlockedUntil).getTime() - Date.now())
    : 0;

  return (
    <GateContainer className={className}>
      <ContentWrapper $blurred={true}>
        {children}
      </ContentWrapper>
      
      <LockedOverlay>
        <LockIcon>
          <FiLock size={24} />
        </LockIcon>
        
        <Title>{getFeatureTitle()}</Title>
        <Description>{getFeatureDescription()}</Description>
        
        {/* Show trial banner if available */}
        {canStartTrial && (
          <TrialBanner>
            <FiGift size={16} />
            {t('premium.trial.banner', '7-day free trial available!')}
          </TrialBanner>
        )}
        
        {/* Show ad unlock banner if active */}
        {hasActiveAdUnlock && adUnlockTimeRemaining > 0 && (
          <AdUnlockBanner>
            <FiStar size={16} />
            {t('premium.adUnlock.active', 'Premium unlocked for {{time}}', {
              time: Math.ceil(adUnlockTimeRemaining / (1000 * 60)) + ' min'
            })}
          </AdUnlockBanner>
        )}
        
        <UnlockOptions>
          {/* Free trial option */}
          {canStartTrial && (
            <Button
              variant="primary"
              onClick={handleStartTrial}
              fullWidth
            >
              <FiGift size={16} />
              {t('premium.actions.startTrial', 'Start 7-Day Free Trial')}
            </Button>
          )}
          
          {/* Watch ad option */}
          {showAdOption && !hasActiveAdUnlock && (
            <Button
              variant="secondary"
              onClick={handleWatchAd}
              disabled={isLoadingAd}
              fullWidth
            >
              <FiPlay size={16} />
              {isLoadingAd 
                ? t('premium.actions.loadingAd', 'Loading ad...')
                : t('premium.actions.watchAd', 'Watch Ad (1 hour unlock)')
              }
            </Button>
          )}
          
          {/* Subscribe option */}
          <Button
            variant="ghost"
            onClick={handleSubscribe}
            fullWidth
          >
            <FiCrown size={16} />
            {subscription.isActive 
              ? t('premium.actions.manage', 'Manage Subscription')
              : t('premium.actions.subscribe', 'Subscribe to Premium')
            }
          </Button>
        </UnlockOptions>
      </LockedOverlay>
    </GateContainer>
  );
};

export default PremiumGate;
