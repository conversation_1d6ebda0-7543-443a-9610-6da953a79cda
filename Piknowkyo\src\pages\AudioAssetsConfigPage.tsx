// src/pages/AudioAssetsConfigPage.tsx

import React, { useState, useEffect, useRef, useContext } from 'react';
import styled, { ThemeContext, DefaultTheme } from 'styled-components';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { 
  FiChevronLeft, FiMusic, FiRadio, FiUpload, FiPlay, FiTrash2, FiLoader, FiAlertCircle,
  FiSquare // Pour l'icône Stop/Pause
} from 'react-icons/fi';
import { fetchAudioAssets, uploadAudioAsset, deleteUserAudioAsset } from '../data/audioAssets'; // Assurez-vous que ce chemin est correct
import { AudioAsset } from '../models'; // Assurez-vous que ce chemin est correct
import ReusableModal from '../components/ReusableModal'; // Assurez-vous que ce chemin est correct

// --- Styled Components de la page ---
const PageContainer = styled.div`
  padding: 1.5rem;
  max-width: 800px;
  margin: 0 auto;
  color: ${({ theme }) => theme.text};
`;

const BackButton = styled.button`
  background: ${({ theme }) => theme.surface}; // Fond plus clair par défaut
  color: ${({ theme }) => theme.primary};    // Couleur primaire pour l'icône
  border: 1px solid ${({ theme }) => theme.borderSlight}; // Bordure subtile
  border-radius: 50%;
  width: 44px; // Légèrement plus grand pour meilleure cible tactile
  height: 44px;
  font-size: 1.3rem; // Icône plus grande
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  margin-bottom: 1.5rem;
  box-shadow: ${({ theme }) => theme.shadowSmall}; // Petite ombre pour le relief
  transition: background-color 0.2s, color 0.2s, transform 0.1s;

  &:hover {
    background: ${({ theme }) => theme.primary};
    color: ${({ theme }) => theme.textLight}; // Texte clair sur fond primaire
    transform: scale(1.05);
  }
  &:focus {
    outline: 2px solid ${({ theme }) => theme.primary};
    outline-offset: 2px;
  }
`;

const PageTitle = styled.h1`
  font-size: 2.2rem;
  color: ${({ theme }) => theme.primary};
  text-align: center;
  margin-bottom: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  svg {
      opacity: 0.8;
  }
`;

const AssetSection = styled.div`
  background: ${({ theme }) => theme.surface};
  border-radius: 16px;
  box-shadow: ${({ theme }) => theme.cardShadow};
  margin-bottom: 2.5rem;
  padding: 1.5rem;
`;

const SectionHeader = styled.h2`
  font-size: 1.5rem;
  color: ${({ theme }) => theme.text};
  margin-top: 0;
  margin-bottom: 1.5rem;
  padding-bottom: 0.8rem;
  border-bottom: 1px solid ${({ theme }) => theme.border};
  display: flex;
  align-items: center;
  gap: 0.75rem;
  svg {
    color: ${({ theme }) => theme.primary};
  }
`;

const AssetList = styled.ul`
  list-style: none;
  padding: 0;
  margin-bottom: 1.5rem;
`;

const AssetItem = styled.li`
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: ${({ theme }) => theme.surfaceAlt};
  border-radius: 10px;
  padding: 0.8rem 1.2rem;
  margin-bottom: 0.8rem;
  font-size: 0.95rem;
  color: ${({ theme }) => theme.textSecondary};
  border: 1px solid ${({ theme }) => theme.border};

  &:last-child {
    margin-bottom: 0;
  }
`;

const AssetName = styled.span`
  flex-grow: 1;
  margin-right: 1rem;
  color: ${({ theme }) => theme.text};
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  word-break: break-word; // Pour les noms de fichiers longs
  svg {
      color: ${({theme}) => theme.accent || theme.primary};
      flex-shrink: 0; // Empêche l'icône de rétrécir
  }
`;

const AssetActions = styled.div`
  display: flex;
  gap: 0.5rem;
  flex-shrink: 0; // Empêche les actions de rétrécir
`;

const ActionButton = styled.button<{ $variant?: 'primary' | 'danger' | 'secondary' }>`
  background: ${({ theme, $variant }) => 
    $variant === 'danger' ? (theme.errorColor || '#d9534f') : 
    $variant === 'secondary' ? (theme.surfaceAlt || '#e9ecef') : 
    (theme.primary || '#007bff')
  };
  color: ${({ theme, $variant }) => 
    $variant === 'secondary' ? (theme.textSecondary || '#495057') : (theme.textLight || '#fff')
  };
  border: ${({ theme, $variant }) => $variant === 'secondary' ? `1px solid ${theme.border || '#ced4da'}` : 'none'};
  border-radius: 6px;
  padding: 0.5rem 0.8rem;
  font-size: 0.8rem;
  font-weight: 500;
  cursor: pointer;
  transition: opacity 0.2s, background-color 0.2s, color 0.2s;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.3rem;
  min-width: 36px; // Assurer une taille minimale pour les boutons avec juste une icône

  &:hover { opacity: 0.85; }
  &:disabled { 
    background: ${({ theme }) => theme.disabled || '#ccc'} !important;
    color: ${({ theme }) => theme.textMuted || '#6c757d'} !important;
    cursor: not-allowed;
    opacity: 0.7;
  }
`;

const UploadForm = styled.div`
  border-top: 1px dashed ${({ theme }) => theme.border};
  padding-top: 1.5rem;
  margin-top: 1.5rem;
  text-align: center;

  p {
    color: ${({ theme }) => theme.textSecondary};
    font-size: 0.9rem;
    margin-bottom: 1rem;
  }

  input[type="file"] {
    display: none;
  }

  label.custom-file-upload {
    background: ${({ theme }) => theme.secondary || theme.accent}; // Fallback pour secondary
    color: ${({ theme }) => theme.textLight};
    padding: 0.8rem 1.5rem;
    border-radius: 8px;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1rem;
    transition: opacity 0.2s;

    &:hover { opacity: 0.9; }
  }

  .file-name {
    margin-top: 0.8rem;
    font-size: 0.9rem;
    color: ${({ theme }) => theme.text};
    font-weight: 500;
    word-break: break-all; // Pour les noms de fichiers longs
  }

  .upload-status {
    margin-top: 0.8rem;
    font-size: 0.9rem;
    color: ${({ theme }) => theme.textMuted};
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    progress {
      flex-grow: 1;
      max-width: 200px;
      height: 8px;
      border-radius: 4px;
      &::-webkit-progress-bar { background-color: ${({ theme }) => theme.surfaceAlt}; border-radius: 4px; }
      &::-webkit-progress-value { background-color: ${({ theme }) => theme.primary}; border-radius: 4px; }
      &::-moz-progress-bar { background-color: ${({ theme }) => theme.primary}; border-radius: 4px; }
    }
  }
`;

const LoadingContainer = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 3rem;
  font-size: 1.2rem;
  color: ${({ theme }) => theme.primary};
  min-height: 50vh;

  svg {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;

const ErrorMessage = styled.div`
  color: ${({ theme }) => theme.errorColor || 'red'}; 
  text-align: center;
  padding: 2rem;
  background-color: ${({ theme }) => `${theme.errorColor}1A` || '#ff00001A'};
  border: 1px solid ${({ theme }) => theme.errorColor || 'red'};
  border-radius: 8px;
  p {
    margin: 0;
  }
`;


const AudioAssetsConfigPage: React.FC = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const theme = useContext(ThemeContext) as DefaultTheme;

  const [musics, setMusics] = useState<AudioAsset[]>([]);
  const [ambiants, setAmbiants] = useState<AudioAsset[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const [musicFileToUpload, setMusicFileToUpload] = useState<File | null>(null);
  const [ambientFileToUpload, setAmbientFileToUpload] = useState<File | null>(null);
  const [uploadingMusic, setUploadingMusic] = useState(false);
  const [uploadingAmbient, setUploadingAmbient] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);

  const [currentlyPlayingUrl, setCurrentlyPlayingUrl] = useState<string | null>(null);
  const currentPlayingAudio = useRef<HTMLAudioElement | null>(null);
  const userId = "testUser123"; // Placeholder: Remplacer par une vraie gestion d'ID utilisateur

  const [showDeleteConfirmModal, setShowDeleteConfirmModal] = useState(false);
  const [assetToDelete, setAssetToDelete] = useState<{ asset: AudioAsset; type: 'musics' | 'ambiants' } | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  useEffect(() => {
    const loadAssets = async () => {
      setIsLoading(true);
      setError(null);
      try {
        // Passer userId ici si votre fetchAudioAssets le prend en compte pour filtrer les assets utilisateur
        const fetchedAssets = await fetchAudioAssets(); 
        setMusics(fetchedAssets.musics);
        setAmbiants(fetchedAssets.ambiants);
      } catch (err) {
        console.error("Error loading audio assets:", err);
        setError(err instanceof Error ? err.message : t('errors.cantLoadAssets', "Impossible de charger les fichiers audio."));
      } finally {
        setIsLoading(false);
      }
    };
    loadAssets();

    return () => { // Cleanup function
      if (currentPlayingAudio.current) {
        currentPlayingAudio.current.pause();
        currentPlayingAudio.current.onended = null;
        currentPlayingAudio.current.onerror = null;
        currentPlayingAudio.current.oncanplaythrough = null;
        currentPlayingAudio.current.src = ''; // Libérer la ressource
        currentPlayingAudio.current = null;
      }
      setCurrentlyPlayingUrl(null);
    };
  }, [t, userId]); // Ajouter userId si fetchAudioAssets l'utilise pour recharger si l'utilisateur change

  const handlePreview = (url: string) => {
    if (currentPlayingAudio.current) {
      currentPlayingAudio.current.pause();
      currentPlayingAudio.current.currentTime = 0;
      currentPlayingAudio.current.onended = null;
      currentPlayingAudio.current.onerror = null;
      currentPlayingAudio.current.oncanplaythrough = null;

      if (currentPlayingAudio.current.src.endsWith(url.split('/').pop()!) && currentlyPlayingUrl === url) {
        currentPlayingAudio.current.src = '';
        currentPlayingAudio.current = null;
        setCurrentlyPlayingUrl(null);
        return;
      }
      currentPlayingAudio.current.src = ''; // Pour être sûr de libérer l'ancien
    }

    const audio = new Audio(url);
    audio.volume = 0.7;
    
    const playAudio = () => {
        audio.play().then(() => {
            setCurrentlyPlayingUrl(url);
        }).catch(e => {
            console.warn("Error playing audio preview:", e);
            alert(t('audioAssets.previewError', "Impossible de lire l'aperçu audio."));
            setCurrentlyPlayingUrl(null);
        });
    };
    
    if (audio.readyState >= HTMLMediaElement.HAVE_ENOUGH_DATA) {
        playAudio();
    } else {
        audio.oncanplaythrough = playAudio;
    }
    
    audio.onended = () => {
      setCurrentlyPlayingUrl(null);
      if (currentPlayingAudio.current === audio) { // S'assurer qu'on ne nettoie pas un autre audio
          currentPlayingAudio.current.src = '';
          currentPlayingAudio.current = null;
      }
    };
    
    audio.onerror = (e) => {
      console.error("Error loading audio for preview:", e);
      alert(t('audioAssets.previewError', "Impossible de charger l'aperçu audio."));
      setCurrentlyPlayingUrl(null);
      if (currentPlayingAudio.current === audio) {
          currentPlayingAudio.current.src = '';
          currentPlayingAudio.current = null;
      }
    };
    currentPlayingAudio.current = audio;
  };

  const handleMusicFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setMusicFileToUpload(e.target.files ? e.target.files[0] : null);
  };
  const handleAmbientFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setAmbientFileToUpload(e.target.files ? e.target.files[0] : null);
  };

  const handleUpload = async (type: 'musics' | 'ambiants', file: File | null) => {
    if (!file) return;
    if (!userId) {
        alert(t('errors.userNotAuthenticated', "Vous devez être connecté pour téléverser des fichiers."));
        return;
    }

    if (type === 'musics') setUploadingMusic(true);
    else setUploadingAmbient(true);

    // Simulation de progression pour l'UI
    setUploadProgress(0);
    let progress = 0;
    const interval = setInterval(() => {
      progress += 10;
      setUploadProgress(Math.min(progress, 100));
      if (progress >= 100) clearInterval(interval);
    }, 100); // Ajuster la vitesse de la simulation

    try {
      const newAsset = await uploadAudioAsset(file, type, userId); 
      if (type === 'musics') {
        setMusics(prev => [...prev, newAsset]);
        setMusicFileToUpload(null);
      } else {
        setAmbiants(prev => [...prev, newAsset]);
        setAmbientFileToUpload(null);
      }
      alert(t('audioAssets.uploadSuccess', 'Fichier {{fileName}} téléversé avec succès !', { fileName: file.name }));
    } catch (err) {
      console.error("Error uploading asset:", err);
      alert(t('audioAssets.uploadError', `Erreur lors du téléversement: ${err instanceof Error ? err.message : String(err)}`));
    } finally {
      if (progress < 100) clearInterval(interval); // S'assurer que l'intervalle est nettoyé
      setUploadProgress(100); // Forcer 100% à la fin
      setTimeout(() => { // Laisser la barre à 100% un instant avant de la cacher
        if (type === 'musics') setUploadingMusic(false);
        else setUploadingAmbient(false);
        setUploadProgress(0); // Réinitialiser pour le prochain upload
      }, 500);
    }
  };

  const requestDeleteUserAsset = (asset: AudioAsset, type: 'musics' | 'ambiants') => {
      if (!asset.isUserUploaded || !asset.storagePath) {
          alert(t('audioAssets.cannotDeleteDefault', "Les fichiers par défaut ne peuvent pas être supprimés."));
          return;
      }
      setAssetToDelete({ asset, type });
      setShowDeleteConfirmModal(true);
  };

  const executeDelete = async () => {
    if (!assetToDelete) return;
    const { asset, type } = assetToDelete;

    if (!userId) {
      alert(t('errors.userNotAuthenticated', "Vous devez être connecté pour supprimer des fichiers."));
      setShowDeleteConfirmModal(false);
      setAssetToDelete(null);
      return;
    }

    setIsDeleting(true);
    try {
        await deleteUserAudioAsset(asset.storagePath!, asset.id, type, userId);
        if (type === 'musics') {
            setMusics(prev => prev.filter(a => a.id !== asset.id));
        } else {
            setAmbiants(prev => prev.filter(a => a.id !== asset.id));
        }
        alert(t('audioAssets.deleteSuccess', 'Fichier supprimé avec succès !'));
    } catch (err) {
        console.error("Error deleting asset:", err);
        alert(t('audioAssets.deleteError', `Erreur lors de la suppression: ${err instanceof Error ? err.message : String(err)}`));
    } finally {
        setIsDeleting(false);
        setShowDeleteConfirmModal(false);
        setAssetToDelete(null);
    }
  };


  if (isLoading) {
    return <LoadingContainer><FiLoader />{t('loading.audioAssets', 'Chargement des fichiers audio...')}</LoadingContainer>;
  }

  if (error) {
    return (
      <PageContainer>
        <BackButton onClick={() => navigate('/settings')} title={t('actions.backToSettings', "Retour aux paramètres") || "Retour"}>
            <FiChevronLeft />
        </BackButton>
        <ErrorMessage><p>{error}</p></ErrorMessage>
      </PageContainer>
    );
  }
  
  const isActionInProgress = uploadingMusic || uploadingAmbient || isDeleting;

  return (
    <PageContainer>
      <BackButton onClick={() => navigate('/settings')} title={t('actions.backToSettings', "Retour aux paramètres") || "Retour"}>
        <FiChevronLeft />
      </BackButton>

      <PageTitle><FiMusic style={{ marginRight: '0.5rem' }} /> {t('audioAssets.title', 'Gérer mes Fichiers Audio')}</PageTitle>

      {/* Section Musiques */}
      <AssetSection>
        <SectionHeader><FiMusic /> {t('audioAssets.musicTitle', 'Musiques de Fond')}</SectionHeader>
        <AssetList>
          {musics.length === 0 && <p style={{color: theme?.textMuted || '#888', textAlign: 'center'}}>{t('audioAssets.noMusics', 'Aucune musique disponible.')}</p>}
          {musics.map(asset => (
            <AssetItem key={asset.id}>
              <AssetName><FiMusic /> {asset.name}</AssetName>
              <AssetActions>
                <ActionButton 
                  onClick={() => handlePreview(asset.url)} 
                  disabled={isActionInProgress}
                  title={currentlyPlayingUrl === asset.url ? t('actions.stopPreview', 'Arrêter') : t('actions.preview', 'Prévisualiser')}
                >
                  {currentlyPlayingUrl === asset.url ? <FiSquare /> : <FiPlay />}
                </ActionButton>
                {asset.isUserUploaded && (
                    <ActionButton 
                        $variant="danger"
                        onClick={() => requestDeleteUserAsset(asset, 'musics')} 
                        disabled={isActionInProgress || (currentlyPlayingUrl === asset.url)}
                        title={t('actions.delete', 'Supprimer') || "Supprimer"}
                    >
                        <FiTrash2 />
                    </ActionButton>
                )}
              </AssetActions>
            </AssetItem>
          ))}
        </AssetList>
        <UploadForm>
          <p>{t('audioAssets.uploadMusicPrompt', 'Ajouter votre propre musique de fond (format MP3, max 10Mo) :')}</p>
          <label htmlFor="music-file-upload" className="custom-file-upload">
            <FiUpload /> {musicFileToUpload ? t('audioAssets.changeFile', 'Changer le fichier') : t('audioAssets.selectFile', 'Sélectionner un fichier')}
          </label>
          <input id="music-file-upload" type="file" accept="audio/mpeg,audio/mp3" onChange={handleMusicFileChange} disabled={isActionInProgress}/>
          {musicFileToUpload && <div className="file-name">{musicFileToUpload.name}</div>}
          {uploadingMusic && (
            <div className="upload-status">
              <FiLoader style={{animation: 'spin 1s linear infinite'}} /> {t('audioAssets.uploading', 'Téléversement en cours...')}
              <progress value={uploadProgress} max="100"></progress>
            </div>
          )}
          {!uploadingMusic && musicFileToUpload && (
            <ActionButton 
              onClick={() => handleUpload('musics', musicFileToUpload)} 
              disabled={uploadingAmbient || isDeleting} // Empêche les uploads simultanés pour cette démo
              style={{marginTop: '1rem'}}
            >
              <FiUpload /> {t('actions.upload', 'Téléverser')}
            </ActionButton>
          )}
        </UploadForm>
      </AssetSection>

      {/* Section Sons Ambiants */}
      <AssetSection>
        <SectionHeader><FiRadio /> {t('audioAssets.ambientTitle', 'Sons Ambiants')}</SectionHeader>
        <AssetList>
          {ambiants.length === 0 && <p style={{color: theme?.textMuted || '#888', textAlign: 'center'}}>{t('audioAssets.noAmbiants', 'Aucun son ambiant disponible.')}</p>}
          {ambiants.map(asset => (
            <AssetItem key={asset.id}>
              <AssetName><FiRadio /> {asset.name}</AssetName>
              <AssetActions>
                <ActionButton 
                  onClick={() => handlePreview(asset.url)} 
                  disabled={isActionInProgress}
                  title={currentlyPlayingUrl === asset.url ? t('actions.stopPreview', 'Arrêter') : t('actions.preview', 'Prévisualiser')}
                >
                  {currentlyPlayingUrl === asset.url ? <FiSquare /> : <FiPlay />}
                </ActionButton>
                {asset.isUserUploaded && (
                    <ActionButton 
                        $variant="danger"
                        onClick={() => requestDeleteUserAsset(asset, 'ambiants')} 
                        disabled={isActionInProgress || (currentlyPlayingUrl === asset.url)}
                        title={t('actions.delete', 'Supprimer') || "Supprimer"}
                    >
                        <FiTrash2 />
                    </ActionButton>
                )}
              </AssetActions>
            </AssetItem>
          ))}
        </AssetList>
        <UploadForm>
          <p>{t('audioAssets.uploadAmbientPrompt', 'Ajouter votre propre son ambiant (format MP3, max 5Mo) :')}</p>
          <label htmlFor="ambient-file-upload" className="custom-file-upload">
            <FiUpload /> {ambientFileToUpload ? t('audioAssets.changeFile', 'Changer le fichier') : t('audioAssets.selectFile', 'Sélectionner un fichier')}
          </label>
          <input id="ambient-file-upload" type="file" accept="audio/mpeg,audio/mp3" onChange={handleAmbientFileChange} disabled={isActionInProgress} />
          {ambientFileToUpload && <div className="file-name">{ambientFileToUpload.name}</div>}
          {uploadingAmbient && (
            <div className="upload-status">
              <FiLoader style={{animation: 'spin 1s linear infinite'}} /> {t('audioAssets.uploading', 'Téléversement en cours...')}
              <progress value={uploadProgress} max="100"></progress>
            </div>
          )}
           {!uploadingAmbient && ambientFileToUpload && (
            <ActionButton 
              onClick={() => handleUpload('ambiants', ambientFileToUpload)} 
              disabled={uploadingMusic || isDeleting} // Empêche les uploads simultanés pour cette démo
              style={{marginTop: '1rem'}}
            >
              <FiUpload /> {t('actions.upload', 'Téléverser')}
            </ActionButton>
          )}
        </UploadForm>
      </AssetSection>

      {/* Modale de Confirmation de Suppression */}
      <ReusableModal
        isOpen={showDeleteConfirmModal && !!assetToDelete}
        onClose={() => { if (!isDeleting) { setShowDeleteConfirmModal(false); setAssetToDelete(null); }}}
        title={t('audioAssets.confirmDeleteTitle', 'Confirmer la Suppression')}
        titleIcon={<FiAlertCircle style={{ color: theme?.errorColor || '#d9534f' }} />}
        isLoading={isDeleting}
        footerContent={
            <>
                <ActionButton $variant="secondary" onClick={() => { setShowDeleteConfirmModal(false); setAssetToDelete(null); }} disabled={isDeleting}>
                    {t('actions.cancel', 'Annuler')}
                </ActionButton>
                <ActionButton $variant="danger" onClick={executeDelete} disabled={isDeleting}>
                    {isDeleting ? <FiLoader style={{ animation: 'spin 1s linear infinite', marginRight: '0.5em' }} /> : <FiTrash2 />}
                    {isDeleting ? t('actions.deleting', 'Suppression...') : t('actions.deleteConfirm', 'Oui, supprimer')}
                </ActionButton>
            </>
        }
      >
        {assetToDelete && (
             <p>
                {t('audioAssets.confirmDeleteMessage', "Êtes-vous sûr de vouloir supprimer le fichier '{{fileName}}' ? Cette action est irréversible.", { fileName: assetToDelete.asset.name })}
            </p>
        )}
      </ReusableModal>
    </PageContainer>
  );
};

export default AudioAssetsConfigPage;