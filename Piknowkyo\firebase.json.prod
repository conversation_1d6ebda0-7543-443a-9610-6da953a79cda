{
  "hosting": {
    "public": "dist",
    "ignore": [
      "firebase.json",
      "**/.*",
      "**/node_modules/**"
    ],
    "rewrites": [
      {
        "source": "**",
        "destination": "/index.html"
      }
    ],
    "headers": [
      {
        "source": "**",
        "headers": [{
          "key": "Cross-Origin-Opener-Policy",
          "value": "same-origin"
        }, {
          "key": "Cross-Origin-Embedder-Policy",
          "value": "require-corp"
        }]
      }
    ],
    "cleanUrls": true,
    "trailingSlash": true,
    "appAssociation": "AUTO",
    "i18n": {
      "root": "/web"
    },
    "redirects": [],
    "frameworksBackend": {
      "region": "europe-west1"
    }
  },
  "storage": {
    "rules": "storage.rules" // Également nécessaire pour le déploiement en production
  }
}