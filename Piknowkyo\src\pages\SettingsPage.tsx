// src/pages/SettingsPage.tsx

import React, { useState, useEffect, useCallback, useContext, useRef } from 'react';
import styled, { ThemeContext, DefaultTheme } from 'styled-components';
import { useTranslation } from 'react-i18next';
import { ttsPlay, TTSConfig, TTSProvider } from '../services/tts';
import { useNavigate } from 'react-router-dom';
import { ttsVoices, TTSVoice } from '../services/ttsVoices'; // Assurez-vous que TTSVoice inclut `disabled?`
import { fetchPiperVoicesByLang, PiperVoice } from '../services/piperVoices';
import NotificationTest from '../components/NotificationTest'; // Composant pour tester les notifications
import ReusableModal from '../components/ReusableModal'; // Importer la modale réutilisable
import { useLang, Language } from '../LangProvider';
import {
    FiSave, FiVolume2, FiDownloadCloud, FiCheckCircle, FiLoader, FiInfo, FiBell, FiAlertTriangle, FiMusic
} from 'react-icons/fi';

// --- Styled Components (Gardez vos styled components tels quels) ---
const PageContainer = styled.div`
  padding: 1.5rem;
  max-width: 800px;
  margin: 0 auto;
  color: ${({ theme }) => theme.text};
`;

const PageTitle = styled.h1`
  font-size: 2rem;
  color: ${({ theme }) => theme.primary};
  text-align: center;
  margin-bottom: 2rem;
`;

const SettingsSection = styled.div`
  background: ${({ theme }) => theme.surface};
  border-radius: 12px;
  box-shadow: ${({ theme }) => theme.cardShadow};
  margin-bottom: 2rem;
  padding: 1.5rem;
`;

const SectionTitle = styled.h2`
  font-size: 1.4rem;
  color: ${({ theme }) => theme.primary};
  margin-top: 0;
  margin-bottom: 1.5rem;
  padding-bottom: 0.8rem;
  border-bottom: 1px solid ${({ theme }) => theme.border};
  display: flex;
  align-items: center;
  gap: 0.5rem;
`;

const FormGroup = styled.div`
  margin-bottom: 1.5rem;
  label {
    display: block;
    font-weight: 500;
    color: ${({ theme }) => theme.textSecondary};
    margin-bottom: 0.5rem;
    font-size: 0.95rem;
  }
  select, input[type="text"] {
    width: 100%;
    padding: 0.7rem;
    border-radius: 8px;
    border: 1px solid ${({ theme }) => theme.border};
    background: ${({ theme }) => theme.inputBackground || theme.surfaceAlt};
    color: ${({ theme }) => theme.text};
    font-size: 1rem;
    &:focus {
      outline: none;
      border-color: ${({ theme }) => theme.primary};
      box-shadow: 0 0 0 2px ${({ theme }) => theme.primary}40;
    }
  }
`;

const Button = styled.button<{ $variant?: 'primary' | 'danger' | 'secondary' }>`
  background: ${({ theme, $variant }) =>
    $variant === 'danger' ? (theme.errorColor || '#d9534f') :
    $variant === 'secondary' ? (theme.surfaceAlt || '#e9ecef') :
    (theme.primary || '#007bff')
  };
  color: ${({ theme, $variant }) =>
    $variant === 'secondary' ? (theme.textSecondary || '#495057') : (theme.textLight || '#fff')
  };
  border: ${({ theme, $variant }) => $variant === 'secondary' ? `1px solid ${theme.border || '#ced4da'}` : 'none'};
  border-radius: 8px;
  padding: 0.8rem 1.5rem;
  font-size: 1rem;
  font-weight: 500;
  margin-top: 1rem;
  margin-right: 0.5rem;
  cursor: pointer;
  transition: background-color 0.2s, transform 0.1s, opacity 0.2s;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;

  &:hover {
    opacity: 0.9;
  }
  &:active {
    transform: scale(0.98);
  }
  &:disabled {
    background: ${({ theme }) => theme.disabled || '#ccc'} !important;
    color: ${({ theme }) => theme.textMuted || '#777'} !important;
    cursor: not-allowed;
    opacity: 0.7;
  }
`;

const InfoText = styled.p`
  font-size: 0.9rem;
  color: ${({ theme }) => theme.textMuted};
  margin-top: 0.5rem;
  line-height: 1.5;
`;

const DownloadStatus = styled.div`
  margin-top: 0.5rem;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;

  &.downloading {
    color: ${({ theme }) => theme.accent || '#e67e22'};
  }
  &.downloaded {
    color: ${({ theme }) => theme.primary || '#27ae60'};
  }
  progress {
    /* width: 100px; // Peut-être mieux de le laisser s'adapter */
    flex-grow: 1;
    max-width: 150px;
    margin-left: 0.5rem;
  }
`;
// --- Fin Styled Components ---


const ttsProvidersList: { id: TTSProvider; label: string }[] = [
  { id: 'browser', label: 'Voix intégrée (on-device)' },
  { id: 'piper', label: 'Piper (offline, voix naturelle)' },
  { id: 'ttswebui', label: 'TTS Generation Web UI (offline)' },
  { id: 'openvoice', label: 'OpenVoice v2 (online)' },
  { id: 'murf', label: 'Murf Speech Gen 2 (online)' },
  { id: 'parlertts', label: 'Parler-TTS (online)' },
];

// Récupère les voix pour un provider spécifique et une langue
// (getVoicesForProvider reste utile si ttsVoices est bien structuré)
const getVoicesForProvider = (providerId: string, lang: string): TTSVoice[] => {
  const providerKey = providerId as keyof typeof ttsVoices;
  if (ttsVoices && ttsVoices[providerKey]) {
    const voicesForProvider = ttsVoices[providerKey] as TTSVoice[];
    return voicesForProvider.filter((v) => v.lang === lang || v.lang === 'auto' || v.lang.startsWith(lang));
  }
  return [];
};

// États pour la modale
type ModalInfo = {
    type: 'success' | 'error' | 'info';
    title: string;
    message: string;
}

const SettingsPage: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { lang: currentAppLang, setLang: setAppLang } = useLang(); // Pour la langue de l'application
  const theme = useContext(ThemeContext) as DefaultTheme;

  // États pour la configuration TTS
  const [provider, setProvider] = useState<TTSProvider>('browser');
  const [voice, setVoice] = useState<string>('auto'); // L'ID de la voix sélectionnée
  const [ttsLang, setTtsLang] = useState<string>(currentAppLang); // Langue pour la voix TTS

  // États pour le téléchargement des voix Piper
  const [downloading, setDownloading] = useState<boolean>(false);
  const [downloaded, setDownloaded] = useState<{[k:string]:boolean}>({}); // Voix Piper téléchargées {voiceId: true}
  const [progress, setProgress] = useState<number>(0);

  // Liste des voix disponibles pour le provider/langue sélectionné
  const [voiceList, setVoiceList] = useState<(TTSVoice | PiperVoice)[]>([]);

  // Références
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const mountedRef = useRef(true);

  // États pour la modale de notification
  const [modalInfo, setModalInfo] = useState<ModalInfo | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);


  useEffect(() => {
    mountedRef.current = true;
    // Nettoyage au démontage
    return () => {
      mountedRef.current = false;
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
      if (window.speechSynthesis) {
          window.speechSynthesis.onvoiceschanged = null;
      }
    };
  }, []);

  // CHARGEMENT INITIAL des paramètres sauvegardés
  useEffect(() => {
    const savedTtsConfig = localStorage.getItem('tts_config_v2');
    if (savedTtsConfig) {
      try {
        const config = JSON.parse(savedTtsConfig);
        // Utiliser les setters pour s'assurer que la logique de mise à jour est déclenchée si nécessaire
        if (ttsProvidersList.find(p => p.id === config.provider)) {
            setProvider(config.provider as TTSProvider);
        }
        // Vérifier la validité de la langue avant de la setter
        if (['fr', 'en', 'es'].includes(config.lang)) {
            setTtsLang(config.lang);
        } else {
            setTtsLang(currentAppLang); // Fallback si invalide
        }
        setVoice(config.voice || 'auto'); // setVoice après setProvider et setTtsLang pour que voiceList soit correcte
      } catch (e) {
        console.error("Erreur de parsing de la config TTS sauvegardée:", e);
        // Fallback aux valeurs par défaut si le parsing échoue
        setProvider('browser');
        setTtsLang(currentAppLang);
        setVoice('auto');
      }
    } else {
      // Si aucune config n'est sauvegardée, initialiser avec les valeurs par défaut
      // (déjà fait par les valeurs initiales des useState, mais explicite ici pour la clarté)
      setTtsLang(currentAppLang);
    }

    const piperDownloadedStatus = localStorage.getItem('piper_voices_status');
    if (piperDownloadedStatus) {
      try {
        setDownloaded(JSON.parse(piperDownloadedStatus));
      } catch (e) {
        console.error("Erreur de parsing de l'état des voix Piper:", e);
        setDownloaded({});
      }
    }
  }, [currentAppLang]); // currentAppLang est une dépendance pour initialiser ttsLang

  // MISE A JOUR DE LA LISTE DES VOIX (lorsque provider ou ttsLang change)
  useEffect(() => {
    let voiceListUpdateTimeout: NodeJS.Timeout | undefined;

    const updateVoiceListLogic = async () => {
        if (!mountedRef.current) return;
        let list: (TTSVoice | PiperVoice)[] = [];

        try {
            if (provider === 'browser') {
                const getBrowserVoices = (): TTSVoice[] => { // Type explicite ici
                    const browserVoices = window.speechSynthesis?.getVoices() || [];
                    let filteredVoices = browserVoices
                        .filter(v => v.lang.startsWith(ttsLang))
                        .map(v_1 => ({ id: v_1.name, label: `${v_1.name} (${v_1.lang})`, lang: v_1.lang }));

                    if (filteredVoices.length === 0 && browserVoices.length > 0) {
                        filteredVoices = [
                            { id: 'no-specific-voice', label: t('settings.noSpecificVoiceForLang', 'Aucune voix spécifique pour cette langue. Voici toutes les voix disponibles:'), lang: 'info', disabled: true } as TTSVoice,
                            ...browserVoices.map(v_2 => ({ id: v_2.name, label: `${v_2.name} (${v_2.lang})`, lang: v_2.lang }))
                        ];
                    }
                    return filteredVoices;
                };

                // Gérer le chargement asynchrone des voix du navigateur
                if (window.speechSynthesis && window.speechSynthesis.getVoices().length === 0) {
                    window.speechSynthesis.onvoiceschanged = () => {
                        if (mountedRef.current) {
                           const newBrowserVoices = getBrowserVoices();
                           setVoiceList(newBrowserVoices);
                           // Après avoir mis à jour la liste, vérifier si la voix actuelle est valide
                           if (!newBrowserVoices.find(v => v.id === voice && !v.disabled)) {
                                const firstValid = newBrowserVoices.find(v => !v.disabled);
                                setVoice(firstValid ? firstValid.id : 'auto');
                           }
                        }
                        window.speechSynthesis.onvoiceschanged = null; // Nettoyer après usage
                    };
                    list = getBrowserVoices(); // Tenter une première fois, pourrait être vide
                } else {
                    list = getBrowserVoices();
                }
            } else if (provider === 'piper') {
                list = await fetchPiperVoicesByLang(ttsLang as 'fr' | 'en' | 'es');
            } else {
               list = getVoicesForProvider(provider, ttsLang); // Fonction utilitaire
            }
        } catch (error) {
            console.error(`Erreur lors de la récupération des voix pour ${provider} / ${ttsLang}:`, error);
            list = []; // Retourner une liste vide en cas d'erreur
        }

        if (mountedRef.current) {
            setVoiceList(list);
            // Si la voix actuellement sélectionnée n'est pas dans la nouvelle liste (ou est désactivée),
            // choisir la première voix valide ou 'auto'.
            if (!list.find(vo => vo.id === voice && !('disabled' in vo && vo.disabled))) {
                const firstValidVoice = list.find(vo => !('disabled' in vo && vo.disabled));
                setVoice(firstValidVoice ? firstValidVoice.id : 'auto');
            }
        }
    };

    // Déclencher la mise à jour de la liste
    // Utiliser un timeout pour éviter des appels trop fréquents si les dépendances changent rapidement
    if (voiceListUpdateTimeout) clearTimeout(voiceListUpdateTimeout);
    voiceListUpdateTimeout = setTimeout(updateVoiceListLogic, 50); // Petit délai

    return () => {
      if (voiceListUpdateTimeout) clearTimeout(voiceListUpdateTimeout);
      // S'assurer que l'event listener est nettoyé si le composant se démonte pendant l'attente des voix
      if (window.speechSynthesis) {
        window.speechSynthesis.onvoiceschanged = null;
      }
    };
  // Les dépendances incluent `voice` pour revalider si la voix sélectionnée devient invalide
  }, [provider, ttsLang, voice, t]); // `t` si les labels des voix dépendent de la traduction (improbable mais sûr)
                                  // `downloaded` n'est pas directement une dépendance pour la *génération* de la liste, mais pour l'affichage des coches.


  const handleProviderChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newProvider = e.target.value as TTSProvider;
    setProvider(newProvider);
    // Réinitialiser la voix et la langue TTS si pertinent lors du changement de provider
    // setTtsLang(currentAppLang); // Ou garder la langue sélectionnée ? À débattre.
    // setVoice('auto'); // Réinitialiser la voix pour que la nouvelle liste soit bien prise en compte
  };

  const handleLangChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setTtsLang(e.target.value);
    // setVoice('auto'); // Optionnel: réinitialiser la voix lors du changement de langue
  };

  const handleVoiceChange = async (e: React.ChangeEvent<HTMLSelectElement>) => {
    const selectedVoiceId = e.target.value;
    const selectedVoiceObject = voiceList.find(v => v.id === selectedVoiceId);
    if (selectedVoiceObject && 'disabled' in selectedVoiceObject && selectedVoiceObject.disabled) {
        return; // Ne pas sélectionner une option désactivée
    }
    setVoice(selectedVoiceId);

    // Logique de téléchargement pour Piper
    if (intervalRef.current) clearInterval(intervalRef.current); // Nettoyer l'intervalle précédent

    if (provider === 'piper' && selectedVoiceId && selectedVoiceId !== 'auto' && !downloaded[selectedVoiceId]) {
      setDownloading(true);
      setProgress(0);
      let currentProgress = 0;
      intervalRef.current = setInterval(() => {
        if (!mountedRef.current) {
            if(intervalRef.current) clearInterval(intervalRef.current);
            return;
        }
        currentProgress += 10; // Simuler la progression
        setProgress(Math.min(currentProgress, 100));
        if (currentProgress >= 100) {
          if(intervalRef.current) clearInterval(intervalRef.current);
          intervalRef.current = null;
          if (mountedRef.current) {
            const newDownloadedStatus = { ...downloaded, [selectedVoiceId]: true };
            setDownloaded(newDownloadedStatus);
            localStorage.setItem('piper_voices_status', JSON.stringify(newDownloadedStatus));
            setDownloading(false);
          }
        }
      }, 200); // Simuler un téléchargement sur 2 secondes
    }
  };

  // Sauvegarde de la configuration
  const handleSave = () => {
    const configToSave = { provider, voice, lang: ttsLang };
    localStorage.setItem('tts_config_v2', JSON.stringify(configToSave));
    setModalInfo({
        type: 'success',
        title: t('settings.modal.saveSuccessTitle', 'Configuration sauvegardée'),
        message: t('settings.modal.saveSuccessMessage', 'Vos paramètres de synthèse vocale ont été sauvegardés avec succès.'),
    });
    setIsModalOpen(true);
  };

  // Test de la voix
  const handleTestVoice = async () => {
    const testText = t('settings.ttsTestText', 'Bonjour, ceci est un test de synthèse vocale.');
    try {
      const config: TTSConfig = { volume: 0.8 }; // Volume de test par défaut
      await ttsPlay(provider, testText, voice, ttsLang, config);
    } catch (e) {
      setModalInfo({
        type: 'error',
        title: t('settings.modal.testErrorTitle', 'Erreur de Test'),
        message: t('settings.ttsTestError', 'Erreur lors du test TTS : {{message}}', { message: (e instanceof Error ? e.message : String(e)) }),
      });
      setIsModalOpen(true);
    }
  };

  // Détermine si les boutons de test/sauvegarde doivent être désactivés
  const isActionDisabled = downloading || (provider === 'piper' && voice && voice !== 'auto' && !downloaded[voice]);

  return (
    <PageContainer>
      <PageTitle>{t('settings.title', 'Paramètres')}</PageTitle>

      {/* Section Synthèse Vocale */}
      <SettingsSection>
        <SectionTitle><FiVolume2 /> {t('settings.ttsSectionTitle', 'Synthèse Vocale (TTS)')}</SectionTitle>
        <FormGroup>
          <label htmlFor="tts-provider">{t('settings.ttsProvider', 'Fournisseur TTS :')}</label>
          <select id="tts-provider" value={provider} onChange={handleProviderChange}>
            {ttsProvidersList.map(p => <option key={p.id} value={p.id}>{p.label}</option>)}
          </select>
          <InfoText>
            {provider === 'browser' && t('settings.ttsProviderInfo.browser')}
            {provider === 'piper' && t('settings.ttsProviderInfo.piper')}
            {/* Ajouter d'autres descriptions pour d'autres providers ici */}
          </InfoText>
        </FormGroup>

        {/* Toujours afficher la sélection de langue TTS car même le provider 'browser' en a besoin pour filtrer */}
        <FormGroup>
            <label htmlFor="tts-lang">{t('settings.language', 'Langue de la voix :')}</label>
            <select id="tts-lang" value={ttsLang} onChange={handleLangChange}>
              <option value="fr">{t('languages.french')}</option>
              <option value="en">{t('languages.english')}</option>
              <option value="es">{t('languages.spanish')}</option>
            </select>
        </FormGroup>

        {/* Sélecteur de voix, affiché si des voix sont disponibles ou si c'est le provider 'browser' */}
        {(voiceList.length > 0 || provider === 'browser') && (
          <FormGroup>
            <label htmlFor="tts-voice">{t('settings.voice', 'Voix :')}</label>
            <select id="tts-voice" value={voice} onChange={handleVoiceChange} disabled={downloading}>
              {/* Option "Automatique" - conditionnée */}
              {(provider === 'browser' || (provider !== 'piper' && voiceList.find(v => v.id ==='auto'))) &&
                <option value="auto">{t('settings.autoVoice', 'Automatique par langue')}</option>
              }

              {voiceList.map((v) => (
                // Ne pas afficher l'option "auto" si elle est déjà gérée ci-dessus
                v.id !== 'auto' && (
                    <option
                        key={v.id}
                        value={v.id}
                        disabled={'disabled' in v && v.disabled === true}
                        style={('disabled' in v && v.disabled === true) ? { fontWeight: 'bold', color: theme?.textMuted || '#aaa' } : {}}
                    >
                    {v.label} {provider === 'piper' && downloaded[v.id] ? '✔️' : ''}
                    </option>
                )
              ))}

              {/* Message si aucune voix n'est disponible (sauf pour browser qui a son propre message 'no-specific-voice') */}
              {voiceList.length === 0 && provider !== 'browser' && provider !== 'piper' && (
                <option value="no-voice" disabled>{t('settings.noVoiceForSelection', 'Aucune voix pour cette sélection')}</option>
              )}
            </select>
            {/* Statut de téléchargement pour Piper */}
            {provider === 'piper' && voice && voice !== 'auto' && !downloaded[voice] && downloading && (
              <DownloadStatus className="downloading">
                <FiLoader style={{ animation: 'spin 1s linear infinite' }} />
                {t('settings.downloadingVoice', 'Téléchargement...')} {progress}%
                <progress value={progress} max="100"></progress>
              </DownloadStatus>
            )}
            {provider === 'piper' && voice && voice !== 'auto' && downloaded[voice] && !downloading && (
              <DownloadStatus className="downloaded">
                <FiCheckCircle /> {t('settings.voiceDownloaded', 'Voix prête à l’emploi !')}
              </DownloadStatus>
            )}
          </FormGroup>
        )}

        <Button
            type="button"
            onClick={handleTestVoice}
            disabled={!!isActionDisabled} // Forcer en booléen explicite
        >
            <FiVolume2 /> {t('settings.testVoice', 'Tester la voix')}
        </Button>
        <Button
            onClick={handleSave}
            disabled={!!isActionDisabled} // Forcer en booléen explicite
            // OU disabled={Boolean(isActionDisabled)}
            style={{ background: theme?.accent || theme?.primary }}
        >
            <FiSave /> {t('settings.saveConfig', 'Sauvegarder')}
        </Button>
      </SettingsSection>

      <SettingsSection>
        <SectionTitle><FiMusic /> {t('settings.audioAssetsManagementTitle', 'Gestion des Fichiers Audio')}</SectionTitle>
        <InfoText>
          {t('settings.audioAssetsInfo', "Ajoutez et gérez vos propres musiques de fond et sons ambiants pour personnaliser vos séances.")}
        </InfoText>
        <Button
            onClick={() => navigate('/settings/audio-assets')}
            style={{ marginTop: '1rem' }}
        >
            {t('settings.goToAudioAssets', 'Gérer mes fichiers audio')}
        </Button>
      </SettingsSection>

      {/* Section Informations sur les Providers TTS */}
      <SettingsSection>
        <SectionTitle><FiInfo /> {t('settings.explanationsTitle', 'Informations sur les Providers TTS')}</SectionTitle>
        <ul>
          <li><b>{t('settings.providerLabels.browser', 'Voix intégrée')}:</b> {t('settings.ttsProviderInfo.browser')}</li>
          <li><b>{t('settings.providerLabels.piper', 'Piper')}:</b> {t('settings.ttsProviderInfo.piper')}</li>
          {/* ... autres informations ... */}
        </ul>
      </SettingsSection>

      {/* Modale Réutilisable pour les notifications */}
      <ReusableModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        title={modalInfo?.title}
        titleIcon={
            modalInfo?.type === 'success' ? <FiCheckCircle style={{ color: theme.primary }} /> :
            modalInfo?.type === 'error' ? <FiAlertTriangle style={{ color: theme.errorColor }} /> :
            <FiInfo style={{ color: theme.accent }} />
        }
        footerContent={ // Un seul bouton "OK" pour ces notifications simples
            <Button onClick={() => setIsModalOpen(false)}>
                {t('actions.ok', 'OK')}
            </Button>
        }
      >
        <p>{modalInfo?.message}</p>
      </ReusableModal>

    </PageContainer>
  );
};

export default SettingsPage;