import { useEffect } from 'react';
import { useAppSelector, useAppDispatch } from '../store/hooks';
import {
  fetchSubscriptionStatus,
  startFreeTrial,
  createSubscription,
  cancelSubscription,
  updateTrialDaysRemaining,
  checkAdUnlockExpiry,
  updatePremiumFeatures,
} from '../store/slices/subscriptionSlice';

export const useSubscription = () => {
  const dispatch = useAppDispatch();
  const subscription = useAppSelector(state => state.subscription);
  const auth = useAppSelector(state => state.auth);

  // Auto-update trial days and ad unlock status
  useEffect(() => {
    const interval = setInterval(() => {
      dispatch(updateTrialDaysRemaining());
      dispatch(checkAdUnlockExpiry());
      dispatch(updatePremiumFeatures());
    }, 60000); // Check every minute

    return () => clearInterval(interval);
  }, [dispatch]);

  // Fetch subscription status when user changes
  useEffect(() => {
    if (auth.user?.uid && !subscription.isLoading) {
      dispatch(fetchSubscriptionStatus(auth.user.uid));
    }
  }, [auth.user?.uid, dispatch, subscription.isLoading]);

  const startTrial = async () => {
    if (!auth.user?.uid) {
      throw new Error('User not authenticated');
    }
    
    return dispatch(startFreeTrial(auth.user.uid));
  };

  const subscribe = async (priceId: string) => {
    if (!auth.user?.uid) {
      throw new Error('User not authenticated');
    }
    
    return dispatch(createSubscription({ 
      userId: auth.user.uid, 
      priceId 
    }));
  };

  const cancelSub = async () => {
    if (!subscription.subscriptionId) {
      throw new Error('No active subscription');
    }
    
    return dispatch(cancelSubscription(subscription.subscriptionId));
  };

  const refreshStatus = async () => {
    if (!auth.user?.uid) {
      throw new Error('User not authenticated');
    }
    
    return dispatch(fetchSubscriptionStatus(auth.user.uid));
  };

  // Helper functions
  const hasFeatureAccess = (feature: keyof typeof subscription.premiumFeatures): boolean => {
    return subscription.premiumFeatures[feature];
  };

  const getAccessType = (): 'free' | 'trial' | 'premium' | 'ad_unlock' => {
    if (subscription.isActive) return 'premium';
    if (subscription.isTrialActive) return 'trial';
    if (subscription.adUnlocks.isActive) return 'ad_unlock';
    return 'free';
  };

  const getTrialTimeRemaining = (): number => {
    return subscription.trialDaysRemaining;
  };

  const getAdUnlockTimeRemaining = (): number => {
    if (!subscription.adUnlocks.isActive || !subscription.adUnlocks.unlockedUntil) {
      return 0;
    }
    
    return Math.max(0, new Date(subscription.adUnlocks.unlockedUntil).getTime() - Date.now());
  };

  const canStartTrial = (): boolean => {
    return !subscription.isTrialActive && 
           subscription.trialStartDate === null && 
           !subscription.isActive;
  };

  const isSubscriptionExpiringSoon = (): boolean => {
    if (!subscription.currentPeriodEnd) return false;
    
    const endDate = new Date(subscription.currentPeriodEnd);
    const now = new Date();
    const daysUntilExpiry = Math.ceil((endDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
    
    return daysUntilExpiry <= 7; // Expiring within 7 days
  };

  return {
    // State
    subscription,
    isLoading: subscription.isLoading,
    isUpdating: subscription.isUpdating,
    error: subscription.error,
    
    // Actions
    startTrial,
    subscribe,
    cancelSub,
    refreshStatus,
    
    // Helpers
    hasFeatureAccess,
    getAccessType,
    getTrialTimeRemaining,
    getAdUnlockTimeRemaining,
    canStartTrial,
    isSubscriptionExpiringSoon,
    
    // Quick access to common checks
    isPremium: subscription.isActive,
    isTrialActive: subscription.isTrialActive,
    hasAdUnlock: subscription.adUnlocks.isActive,
    isFree: !subscription.isActive && !subscription.isTrialActive && !subscription.adUnlocks.isActive,
  };
};

export default useSubscription;
