import React, { useState } from 'react';
import styled from 'styled-components';
import { useTranslation } from 'react-i18next';
import { FiPlay, FiStar, FiClock, FiGift, FiX } from 'react-icons/fi';
import { useAds } from '../hooks/useAds';
import { useSubscription } from '../hooks/useSubscription';
import Button from './ui/Button';

const ModalOverlay = styled.div<{ $visible: boolean }>`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: ${({ $visible }) => $visible ? 1 : 0};
  visibility: ${({ $visible }) => $visible ? 'visible' : 'hidden'};
  transition: all 0.3s ease;
  padding: 1rem;
`;

const ModalContent = styled.div`
  background: ${({ theme }) => theme.surface};
  border-radius: 16px;
  padding: 2rem;
  max-width: 400px;
  width: 100%;
  text-align: center;
  position: relative;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
`;

const CloseButton = styled.button`
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: none;
  border: none;
  color: ${({ theme }) => theme.textSecondary};
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;

  &:hover {
    background: ${({ theme }) => theme.border};
    color: ${({ theme }) => theme.text};
  }
`;

const IconContainer = styled.div`
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, #ffd700, #ffed4e);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
  color: #000;
`;

const Title = styled.h2`
  margin: 0 0 1rem 0;
  font-size: 1.5rem;
  font-weight: 700;
  color: ${({ theme }) => theme.text};
`;

const Description = styled.p`
  margin: 0 0 1.5rem 0;
  color: ${({ theme }) => theme.textSecondary};
  line-height: 1.5;
  font-size: 0.95rem;
`;

const RewardList = styled.ul`
  list-style: none;
  padding: 0;
  margin: 0 0 2rem 0;
  text-align: left;
`;

const RewardItem = styled.li`
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background: ${({ theme }) => theme.background};
  border-radius: 8px;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
  color: ${({ theme }) => theme.text};

  &:last-child {
    margin-bottom: 0;
  }
`;

const RewardIcon = styled.div`
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: ${({ theme }) => theme.primary};
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
`;

const Duration = styled.div`
  background: linear-gradient(135deg, #4ade80, #22c55e);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-weight: 600;
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
`;

const Actions = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
`;

const LoadingText = styled.div`
  color: ${({ theme }) => theme.textSecondary};
  font-size: 0.875rem;
  margin-top: 0.5rem;
`;

const ErrorText = styled.div`
  color: #ef4444;
  font-size: 0.875rem;
  margin-top: 0.5rem;
`;

interface AdRewardModalProps {
  isOpen: boolean;
  onClose: () => void;
  feature?: string;
}

const AdRewardModal: React.FC<AdRewardModalProps> = ({
  isOpen,
  onClose,
  feature
}) => {
  const { t } = useTranslation();
  const { watchAdForPremium, isLoading, error, clearError } = useAds();
  const { hasAdUnlock, getAdUnlockTimeRemaining } = useSubscription();
  const [isWatching, setIsWatching] = useState(false);

  const handleWatchAd = async () => {
    setIsWatching(true);
    clearError();

    try {
      const success = await watchAdForPremium();
      
      if (success) {
        // Close modal after successful ad watch
        setTimeout(() => {
          onClose();
        }, 1500);
      }
    } catch (error) {
      console.error('Failed to watch ad:', error);
    } finally {
      setIsWatching(false);
    }
  };

  const handleClose = () => {
    if (!isWatching && !isLoading) {
      clearError();
      onClose();
    }
  };

  const formatTimeRemaining = (milliseconds: number): string => {
    const minutes = Math.ceil(milliseconds / (1000 * 60));
    if (minutes < 60) {
      return t('time.minutes', '{{count}} min', { count: minutes });
    }
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    if (remainingMinutes === 0) {
      return t('time.hours', '{{count}} hour', { count: hours });
    }
    return t('time.hoursMinutes', '{{hours}}h {{minutes}}m', { 
      hours, 
      minutes: remainingMinutes 
    });
  };

  const premiumFeatures = [
    {
      icon: <FiStar size={12} />,
      text: t('premium.features.games.short', 'Mindfulness Games')
    },
    {
      icon: <FiStar size={12} />,
      text: t('premium.features.binaural.short', 'Binaural Beats')
    },
    {
      icon: <FiStar size={12} />,
      text: t('premium.features.ambient.short', 'Ambient Sounds')
    },
    {
      icon: <FiStar size={12} />,
      text: t('premium.features.advanced.short', 'Advanced Sessions')
    },
    {
      icon: <FiStar size={12} />,
      text: t('premium.features.custom.short', 'Custom Audio')
    }
  ];

  // If user already has ad unlock active
  if (hasAdUnlock) {
    const timeRemaining = getAdUnlockTimeRemaining();
    
    return (
      <ModalOverlay $visible={isOpen} onClick={handleClose}>
        <ModalContent onClick={(e) => e.stopPropagation()}>
          <CloseButton onClick={handleClose}>
            <FiX size={20} />
          </CloseButton>
          
          <IconContainer>
            <FiGift size={32} />
          </IconContainer>
          
          <Title>{t('adReward.active.title', 'Premium Unlocked!')}</Title>
          
          <Description>
            {t('adReward.active.description', 'You currently have premium features unlocked.')}
          </Description>
          
          <Duration>
            <FiClock size={16} />
            {t('adReward.active.timeRemaining', 'Time remaining: {{time}}', {
              time: formatTimeRemaining(timeRemaining)
            })}
          </Duration>
          
          <Button variant="primary" onClick={handleClose} fullWidth>
            {t('common.continue', 'Continue')}
          </Button>
        </ModalContent>
      </ModalOverlay>
    );
  }

  return (
    <ModalOverlay $visible={isOpen} onClick={handleClose}>
      <ModalContent onClick={(e) => e.stopPropagation()}>
        <CloseButton onClick={handleClose} disabled={isWatching || isLoading}>
          <FiX size={20} />
        </CloseButton>
        
        <IconContainer>
          <FiPlay size={32} />
        </IconContainer>
        
        <Title>{t('adReward.title', 'Watch Ad for Premium')}</Title>
        
        <Description>
          {t('adReward.description', 'Watch a short ad to unlock all premium features for 1 hour.')}
        </Description>
        
        <Duration>
          <FiClock size={16} />
          {t('adReward.duration', '1 Hour Premium Access')}
        </Duration>
        
        <RewardList>
          {premiumFeatures.map((feature, index) => (
            <RewardItem key={index}>
              <RewardIcon>{feature.icon}</RewardIcon>
              {feature.text}
            </RewardItem>
          ))}
        </RewardList>
        
        <Actions>
          <Button
            variant="primary"
            onClick={handleWatchAd}
            disabled={isWatching || isLoading}
            fullWidth
          >
            <FiPlay size={16} />
            {isWatching 
              ? t('adReward.watching', 'Watching Ad...')
              : t('adReward.watchNow', 'Watch Ad Now')
            }
          </Button>
          
          <Button
            variant="ghost"
            onClick={handleClose}
            disabled={isWatching || isLoading}
            fullWidth
          >
            {t('common.cancel', 'Cancel')}
          </Button>
        </Actions>
        
        {isLoading && (
          <LoadingText>
            {t('adReward.loading', 'Loading ad...')}
          </LoadingText>
        )}
        
        {error && (
          <ErrorText>
            {t('adReward.error', 'Failed to load ad. Please try again.')}
          </ErrorText>
        )}
      </ModalContent>
    </ModalOverlay>
  );
};

export default AdRewardModal;
