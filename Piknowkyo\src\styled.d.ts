// src/styled.d.ts

import 'styled-components';

declare module 'styled-components' {
  export interface DefaultTheme {
    // --- Propriétés existantes (vérifiez qu'elles correspondent à vos thèmes) ---
    name?: string;

    background: string;
    surface: string;
    surfaceAlt: string;

    primary: string;
    secondary: string;
    accent: string;
    primaryHover?: string; // NOUVEAU: pour l'état de survol du bouton primaire
    accentHover?: string;  // NOUVEAU: pour l'état de survol des éléments d'accent

    text: string;
    textSecondary: string;
    textLight: string;
    textMuted?: string;
    textLightOnPrimary?: string;

    border: string;
    
    cardShadow: string;
    headerShadow: string;

    logoBg?: string;
    navActive?: string;
    navInactive?: string;
    
    gradientStart?: string;
    gradientEnd?: string;
    primaryGradient?: string;

    inputBackground?: string;
    errorColor?: string;
    successColor?: string;      // NOUVEAU: Couleur pour les messages de succès
    
    disabledBackground?: string; // NOUVEAU: Fond pour les éléments désactivés (remplace 'disabled')
    disabledText?: string;       // NOUVEAU: Texte pour les éléments désactivés

    cardHoverShadow?: string;
    
    heroOverlay?: string;

    borderSlight: string;
    shadowSmall: string;

    // --- NOUVELLES Propriétés pour les boutons Google ---
    googleButtonBackground?: string;
    googleButtonText?: string;
    googleButtonBorder?: string;
    googleButtonHoverBackground?: string;

    // --- Propriétés Zen Tetris ---
    zenTetrisPiece1?: string;
    zenTetrisPiece2?: string;
    zenTetrisPiece3?: string;
    zenTetrisPiece4?: string;
    zenTetrisPiece5?: string;
    zenTetrisPiece6?: string;
    zenTetrisPiece7?: string;
    zenTetrisBackgroundCell?: string;
    zenTetrisBoardBackground?: string;
    hoverShadow?: string;
  }
}