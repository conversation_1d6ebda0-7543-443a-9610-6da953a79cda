// src/pages/BlogPage.tsx

import React, { useEffect, useState } from 'react';
import styled, {  } from 'styled-components';
import { Link } from 'react-router-dom'; // Pour d'éventuels liens vers des posts uniques
import { useTranslation } from 'react-i18next';
import { useLang } from '../LangProvider'; // Si la langue influence les posts affichés
// import { useAuth } from '../AuthContext'; // Supposons un AuthContext pour l'utilisateur connecté
import {
  FiMessageSquare, FiSend, FiUser, FiEdit, FiThumbsUp, FiMessageCircle as FiCommentIcon, FiClock, FiLoader, FiAlertCircle, FiUserX
} from 'react-icons/fi';
// Firebase imports (à décommenter et configurer quand Firebase est prêt)
// import { db } from '../services/firebase'; // Votre instance Firestore
// import { collection, addDoc, query, orderBy, onSnapshot, serverTimestamp, where, getDocs, Timestamp, doc, updateDoc, arrayUnion, arrayRemove, getDoc, deleteDoc } from "firebase/firestore";
// import { getAuth, onAuthStateChanged, User } from "firebase/auth";


// --- Types ---
interface BlogPost {
  id: string;
  authorId: string; // ID de l'utilisateur Firebase (sera anonymisé à l'affichage)
  authorPseudo: string; // Pseudo anonymisé généré
  content: string;
  category: string; // Catégorie du post
  tags: string[];
  createdAt: any; // Sera un Timestamp Firebase, mais any pour la simulation
  likes: string[]; // Array d'UIDs des utilisateurs qui ont liké
  commentCount: number;
}

// Placeholder pour l'utilisateur (à remplacer par votre AuthContext)
interface CurrentUser {
  uid: string;
  displayName?: string | null; // Ou un pseudo que vous gérez
  // ... autres propriétés de l'utilisateur
}

// --- Styled Components ---

const PageContainer = styled.div`
  padding: 1.5rem;
  max-width: 800px;
  margin: 0 auto;
  color: ${({ theme }) => theme.text};
`;

const PageHeader = styled.div`
  text-align: center;
  margin-bottom: 2.5rem;
  h1 {
    font-size: 2.4rem;
    color: ${({ theme }) => theme.primary};
    margin-bottom: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
  }
  p {
    font-size: 1rem;
    color: ${({ theme }) => theme.textSecondary};
    line-height: 1.6;
  }
`;

const ControlsBar = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 2rem;
  padding: 1rem;
  background-color: ${({ theme }) => theme.surfaceAlt};
  border-radius: 12px;
`;

const SearchInput = styled.input`
  flex-grow: 1;
  min-width: 200px;
  padding: 0.7rem 1rem;
  border-radius: 8px;
  border: 1px solid ${({ theme }) => theme.border};
  background: ${({ theme }) => theme.inputBackground};
  color: ${({ theme }) => theme.text};
  font-size: 1rem;
  &:focus { /* ... (styles de focus) ... */ }
`;

const CategorySelect = styled.select`
  padding: 0.7rem 1rem;
  border-radius: 8px;
  border: 1px solid ${({ theme }) => theme.border};
  background: ${({ theme }) => theme.inputBackground};
  color: ${({ theme }) => theme.text};
  font-size: 1rem;
  min-width: 180px;
  &:focus { /* ... (styles de focus) ... */ }
`;


const PostFormCard = styled.div`
  background: ${({ theme }) => theme.surface};
  border-radius: 12px;
  box-shadow: ${({ theme }) => theme.cardShadow};
  margin-bottom: 2.5rem;
  padding: 1.5rem;
`;

const SectionTitle = styled.h2`
  font-size: 1.4rem;
  color: ${({ theme }) => theme.primary};
  margin-top: 0;
  margin-bottom: 1.5rem; /* Peut être ajusté par style en ligne si besoin */
  padding-bottom: 0.8rem;
  border-bottom: 1px solid ${({ theme }) => theme.border};
  display: flex;
  align-items: center;
  gap: 0.5rem;
`;

const TextArea = styled.textarea`
  width: 100%;
  min-height: 100px;
  border-radius: 8px;
  border: 1px solid ${({ theme }) => theme.border};
  padding: 0.8rem 1rem;
  font-size: 1rem;
  margin-bottom: 1rem;
  resize: vertical;
  background: ${({ theme }) => theme.inputBackground};
  color: ${({ theme }) => theme.text};
  &:focus { /* ... (styles de focus) ... */ }
`;

const FormActions = styled.div`
  display: flex;
  justify-content: space-between; /* Pour espacer le select et le bouton */
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
`;

const CategoryInputGroup = styled.div`
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    min-width: 200px;
    label {
        font-size: 0.9rem;
        color: ${({ theme }) => theme.textSecondary};
    }
`;


const Button = styled.button` /* ... (styles de bouton comme dans SettingsPage) ... */ `;

const PostCard = styled.div`
  background: ${({ theme }) => theme.surface};
  border-radius: 12px;
  box-shadow: ${({ theme }) => theme.cardShadow};
  margin-bottom: 1.5rem;
  padding: 1.5rem;
  border-left: 4px solid ${({ theme }) => theme.primary};
`;

const PostHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
  color: ${({ theme }) => theme.textSecondary};
  font-size: 0.85rem;
`;

const AuthorPseudo = styled.span`
  font-weight: 600;
  color: ${({ theme }) => theme.accent};
  display: flex;
  align-items: center;
  gap: 0.3rem;
  svg { opacity: 0.8; }
`;

const PostDate = styled.span`
  display: flex;
  align-items: center;
  gap: 0.3rem;
`;

const PostContent = styled.p`
  font-size: 1rem;
  line-height: 1.7;
  color: ${({ theme }) => theme.text};
  white-space: pre-wrap; /* Respecter les retours à la ligne */
  margin-bottom: 1rem;
`;

const PostFooter = styled.div`
  display: flex;
  align-items: center;
  gap: 1.5rem;
  font-size: 0.9rem;
  color: ${({ theme }) => theme.textMuted};
  padding-top: 1rem;
  border-top: 1px solid ${({ theme }) => theme.border}80;
`;

const ActionButton = styled.button<{ $liked?: boolean }>`
  background: none;
  border: none;
  color: ${({ theme, $liked }) => $liked ? theme.primary : theme.textMuted};
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.4rem;
  font-size: 0.9rem;
  padding: 0.3rem 0.5rem;
  border-radius: 6px;
  transition: color 0.2s, background-color 0.2s;

  &:hover {
    color: ${({ theme }) => theme.primary};
    background-color: ${({ theme }) => theme.primary}1A;
  }
  svg {
    font-size: 1.1rem;
  }
`;

const LoadingContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  text-align: center;
  color: ${({ theme }) => theme.primary};
  min-height: 70vh; /* Occupe plus de place */
  svg { font-size: 3.5rem; margin-bottom: 1.5rem; animation: spin 1s linear infinite; }
  @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
`;

const ErrorMessage = styled.div`
  color: ${({ theme }) => theme.errorColor || 'red'};
  text-align: center;
  padding: 2rem;
  background-color: ${({ theme }) => theme.surfaceAlt};
  border-radius: 8px;
  border: 1px solid ${({ theme }) => theme.errorColor || 'red'}33;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  min-height: 50vh;
  justify-content: center;

  p { margin-bottom: 1rem; }
  a {
    color: ${({ theme }) => theme.primary};
    text-decoration: underline;
    font-weight: 500;
  }
`;

const InfoMessage = styled.div`
  text-align: center;
  padding: 2rem;
  color: ${({ theme }) => theme.textSecondary};
  background-color: ${({ theme }) => theme.surfaceAlt};
  border-radius: 12px;
  font-style: italic;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  svg {
    font-size: 2.5rem;
    opacity: 0.7;
  }
`;

const ALL_CATEGORIES = ["général", "gratitude", "défis", "inspirations", "questions"]; // Exemple

// Fonction pour générer un pseudo anonyme simple (à améliorer pour plus d'unicité si besoin)
const generateAnonymousPseudo = (uid: string) => {
    const animals = ["Loup", "Aigle", "Renard", "Ours", "Cerf", "Hibou", "Faucon", "Lion", "Tigre", "Panthère"];
    const adjectives = ["Serein", "Sage", "Curieux", "Fort", "Paisible", "Lumineux", "Agile", "Intrépide", "Calme", "Créatif"];
    // Simple hash pour un peu de consistance, mais pas cryptographiquement sécurisé
    const hash = uid.split("").reduce((acc, char) => acc + char.charCodeAt(0), 0);
    return `${adjectives[hash % adjectives.length]} ${animals[hash % animals.length]}`;
};


const BlogPage: React.FC = () => {
  const { t } = useTranslation();
  const { lang } = useLang(); // Pour filtrer les posts par langue si nécessaire plus tard
  // const { currentUser } = useAuth(); // À décommenter quand AuthContext est prêt
  const currentUser: CurrentUser | null = { uid: "testUser123", displayName: "Moi" }; // Placeholder

  const [posts, setPosts] = useState<BlogPost[]>([]);
  const [newPostContent, setNewPostContent] = useState('');
  const [newPostCategory, setNewPostCategory] = useState<string>(ALL_CATEGORIES[0]);
  const [isLoading, setIsLoading] = useState(true);
  const [error] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');


  // Simuler le chargement des posts depuis Firebase
  useEffect(() => {
    setIsLoading(true);
    // const auth = getAuth();
    // const unsubscribeAuth = onAuthStateChanged(auth, (user) => {
    //   if (user) {
    //     setCurrentUser({ uid: user.uid, displayName: user.displayName });
    //   } else {
    //     setCurrentUser(null);
    //   }
    // });

    // const postsCollection = collection(db, "blogPosts");
    // let q = query(postsCollection, orderBy("createdAt", "desc"));
    // if (selectedCategory) {
    //   q = query(q, where("category", "==", selectedCategory));
    // }
    // // Filtrage par langue si vos posts ont un champ 'language'
    // // q = query(q, where("language", "==", lang));


    // const unsubscribeSnapshot = onSnapshot(q, (snapshot) => {
    //   const fetchedPosts = snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as BlogPost));
    //   // Simuler un filtre de recherche côté client pour la démo (Firebase peut le faire mieux)
    //   const filtered = searchTerm
    //     ? fetchedPosts.filter(p =>
    //         p.content.toLowerCase().includes(searchTerm.toLowerCase()) ||
    //         p.authorPseudo.toLowerCase().includes(searchTerm.toLowerCase()) ||
    //         p.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
    //       )
    //     : fetchedPosts;
    //   setPosts(filtered);
    //   setIsLoading(false);
    // }, (err) => {
    //   console.error("Error fetching blog posts:", err);
    //   setError(t('errors.cantLoadBlogPosts', "Impossible de charger les messages du blog."));
    //   setIsLoading(false);
    // });

    // Simulation de données en attendant Firebase
    setTimeout(() => {
      const fakePosts: BlogPost[] = [
        { id: '1', authorId: 'uid1', authorPseudo: generateAnonymousPseudo('uid1'), content: "Première journée avec l'application, je me sens déjà plus calme après la méditation sur la gratitude !", category: "gratitude", tags: ["débutant", "calme"], createdAt: { seconds: Date.now()/1000 - 3600, nanoseconds: 0 }, likes: ['uid2'], commentCount: 1 },
        { id: '2', authorId: 'uid2', authorPseudo: generateAnonymousPseudo('uid2'), content: "Quelqu'un a des astuces pour rester constant dans sa pratique quotidienne ? #motivation", category: "défis", tags: ["motivation", "routine"], createdAt: { seconds: Date.now()/1000 - 7200, nanoseconds: 0 }, likes: [], commentCount: 0 },
        { id: '3', authorId: 'uid3', authorPseudo: generateAnonymousPseudo('uid3'), content: "L'hypnose pour la confiance en soi a vraiment changé ma perspective. Je recommande !", category: "inspirations", tags: ["hypnose", "confiance"], createdAt: { seconds: Date.now()/1000 - 10800, nanoseconds: 0 }, likes: ['uid1', 'uid2', 'uid4'], commentCount: 2 },
      ];
      let filtered = fakePosts;
      if (selectedCategory) {
        filtered = filtered.filter(p => p.category === selectedCategory);
      }
      if (searchTerm) {
        filtered = filtered.filter(p =>
            p.content.toLowerCase().includes(searchTerm.toLowerCase()) ||
            p.authorPseudo.toLowerCase().includes(searchTerm.toLowerCase()) ||
            (p.tags || []).some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
          );
      }
      setPosts(filtered.sort((a,b) => b.createdAt.seconds - a.createdAt.seconds));
      setIsLoading(false);
    }, 1000);

    // return () => {
    //   unsubscribeAuth();
    //   unsubscribeSnapshot();
    // };
  }, [lang, t, selectedCategory, searchTerm]); // currentUser aussi si utilisé

  const handlePostSubmit = async () => {
    if (!newPostContent.trim() || !currentUser) return;
    setIsSubmitting(true);
    const newPost: Omit<BlogPost, 'id' | 'createdAt'> = { // Omit id et createdAt, Firebase les gère
      authorId: currentUser.uid,
      authorPseudo: generateAnonymousPseudo(currentUser.uid), // Anonymisation
      content: newPostContent,
      category: newPostCategory,
      tags: newPostContent.toLowerCase().match(/#\w+/g)?.map(tag => tag.substring(1)) || [], // Extrait les hashtags
      likes: [],
      commentCount: 0,
      // createdAt: serverTimestamp(), // Pour Firebase
    };

    try {
      // await addDoc(collection(db, "blogPosts"), newPost);
      console.log("Nouveau post (simulation):", newPost);
      // Pour la démo, on ajoute au début de la liste existante
      setPosts(prev => [{ ...newPost, id: Date.now().toString(), createdAt: { seconds: Date.now()/1000, nanoseconds: 0 } } as BlogPost, ...prev]);
      setNewPostContent('');
      setNewPostCategory(ALL_CATEGORIES[0]);
    } catch (error) {
      console.error("Error adding new post:", error);
      alert(t('errors.cantAddPost', "Erreur lors de la publication du message."));
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleLikePost = async (postId: string) => {
    if (!currentUser) return;
    // const postRef = doc(db, "blogPosts", postId);
    // const postDoc = await getDoc(postRef);
    // if (postDoc.exists()) {
    //   const postData = postDoc.data() as BlogPost;
    //   if (postData.likes.includes(currentUser.uid)) {
    //     await updateDoc(postRef, { likes: arrayRemove(currentUser.uid) });
    //   } else {
    //     await updateDoc(postRef, { likes: arrayUnion(currentUser.uid) });
    //   }
    // }
    console.log(`Like/Unlike post ${postId} par ${currentUser.uid} (simulation)`);
    // Simuler le like/unlike pour la démo
    setPosts(prevPosts => prevPosts.map(p => {
        if (p.id === postId) {
            const liked = p.likes.includes(currentUser.uid);
            return {
                ...p,
                likes: liked ? p.likes.filter(uid => uid !== currentUser.uid) : [...p.likes, currentUser.uid]
            };
        }
        return p;
    }));
  };

  const formatDate = (timestamp: any) => { // Accepte any pour le timestamp simulé
    if (!timestamp || typeof timestamp.seconds !== 'number') return t('blog.unknownDate', 'Date inconnue');
    return new Date(timestamp.seconds * 1000).toLocaleDateString(lang, {
      year: 'numeric', month: 'long', day: 'numeric', hour: '2-digit', minute: '2-digit'
    });
  };


  if (isLoading && posts.length === 0) { // Afficher le loader seulement au chargement initial
    return <LoadingContainer><FiLoader /> {t('loading.blog', 'Chargement du blog...')}</LoadingContainer>;
  }

  if (error) {
    return <ErrorMessage><FiAlertCircle size={30} /><p>{error}</p><Link to="/">{t('actions.backToHome', "Retour à l'accueil")}</Link></ErrorMessage>;
  }

  return (
    <PageContainer>
      <PageHeader>
        <h1><FiMessageSquare /> {t('blog.title', 'Journal Communautaire')}</h1>
        <p>{t('blog.description', 'Partagez vos expériences, découvertes et inspirations avec la communauté PiKnowKyo. Tous les messages sont anonymes.')}</p>
      </PageHeader>

      <ControlsBar>
        <SearchInput
            type="text"
            placeholder={t('blog.searchPlaceholder', "Rechercher des messages...") || "Rechercher..."}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
        />
        <CategorySelect value={selectedCategory} onChange={(e) => setSelectedCategory(e.target.value)}>
            <option value="">{t('blog.allCategories', "Toutes catégories")}</option>
            {ALL_CATEGORIES.map(cat => (
                <option key={cat} value={cat}>{t(`blog.categories.${cat}`, cat.charAt(0).toUpperCase() + cat.slice(1))}</option>
            ))}
        </CategorySelect>
      </ControlsBar>

      {currentUser ? (
        <PostFormCard>
          <SectionTitle style={{borderBottom: 'none', marginBottom: '1rem'}}><FiEdit /> {t('blog.writeNewPost', 'Écrire un nouveau message')}</SectionTitle>
          <TextArea
            placeholder={t('blog.postPlaceholder', "Votre message (sera publié anonymement)...") || "Votre message..."}
            value={newPostContent}
            onChange={e => setNewPostContent(e.target.value)}
            rows={4}
          />
          <FormActions>
            <CategoryInputGroup>
                <label htmlFor="post-category">{t('blog.category', 'Catégorie')}:</label>
                <CategorySelect
                    id="post-category"
                    value={newPostCategory}
                    onChange={(e) => setNewPostCategory(e.target.value)}
                    style={{minWidth: '150px'}}
                >
                    {ALL_CATEGORIES.map(cat => (
                        <option key={cat} value={cat}>{t(`blog.categories.${cat}`, cat.charAt(0).toUpperCase() + cat.slice(1))}</option>
                    ))}
                </CategorySelect>
            </CategoryInputGroup>
            <Button onClick={handlePostSubmit} disabled={isSubmitting || !newPostContent.trim()}>
              {isSubmitting ? <FiLoader style={{animation: 'spin 1s linear infinite'}} /> : <FiSend />}
              {isSubmitting ? t('blog.publishing', 'Publication...') : t('blog.publish', 'Publier')}
            </Button>
          </FormActions>
        </PostFormCard>
      ) : (
        <InfoMessage>
          <FiUserX />
          {t('blog.loginToPost', 'Vous devez être connecté pour publier un message.')}
          {/* <Link to="/profile">Se connecter / S'inscrire</Link>  Ajoutez un lien de connexion */}
        </InfoMessage>
      )}


      {posts.length === 0 && !isLoading && (
        <InfoMessage>
          <FiMessageSquare />
          {t('blog.noPostsYet', 'Aucun message pour le moment dans cette catégorie ou correspondant à votre recherche.')}
        </InfoMessage>
      )}

      {posts.map((post) => (
        <PostCard key={post.id}>
          <PostHeader>
            <AuthorPseudo><FiUser size={16}/> {post.authorPseudo}</AuthorPseudo>
            <PostDate><FiClock size={14}/> {formatDate(post.createdAt)}</PostDate>
          </PostHeader>
          <PostContent>{post.content}</PostContent>
          <PostFooter>
            <ActionButton
                onClick={() => handleLikePost(post.id)}
                $liked={currentUser && post.likes.includes(currentUser.uid)}
                title={t('blog.like', 'Aimer') || 'Aimer'}
            >
              <FiThumbsUp /> {post.likes.length}
            </ActionButton>
            <ActionButton as={Link} to={`/blog/${post.id}/comments`} title={t('blog.comments', 'Commentaires') || 'Commentaires'}> {/* Lien vers une page de commentaires */}
              <FiCommentIcon /> {post.commentCount || 0}
            </ActionButton>
            {/* Ajouter des tags si pertinent */}
            {/* <div>{post.tags.map(tag => `#${tag} `)}</div> */}
          </PostFooter>
        </PostCard>
      ))}
    </PageContainer>
  );
};

export default BlogPage;