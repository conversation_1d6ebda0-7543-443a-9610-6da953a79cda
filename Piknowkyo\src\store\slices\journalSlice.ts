import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { JournalEntry } from '../../models';

export interface JournalState {
  entries: JournalEntry[];
  isLoading: boolean;
  error: string | null;
  lastSyncTimestamp: number | null;
  pendingChanges: {
    created: JournalEntry[];
    updated: JournalEntry[];
    deleted: string[];
  };
}

const initialState: JournalState = {
  entries: [],
  isLoading: false,
  error: null,
  lastSyncTimestamp: null,
  pendingChanges: {
    created: [],
    updated: [],
    deleted: [],
  },
};

export const fetchJournalEntries = createAsyncThunk(
  'journal/fetchEntries',
  async (_, { rejectWithValue }) => {
    try {
      // Pour l'instant, retourner un tableau vide
      return [];
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

const journalSlice = createSlice({
  name: 'journal',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    updateLastSyncTimestamp: (state, action: PayloadAction<number>) => {
      state.lastSyncTimestamp = action.payload;
    },
    clearPendingChanges: (state) => {
      state.pendingChanges = {
        created: [],
        updated: [],
        deleted: [],
      };
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchJournalEntries.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchJournalEntries.fulfilled, (state, action) => {
        state.isLoading = false;
        state.entries = action.payload;
        state.lastSyncTimestamp = Date.now();
      })
      .addCase(fetchJournalEntries.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
  },
});

export const {
  clearError,
  updateLastSyncTimestamp,
  clearPendingChanges,
} = journalSlice.actions;

export default journalSlice.reducer;
