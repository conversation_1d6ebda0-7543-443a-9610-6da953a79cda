import React from 'react';
import styled from 'styled-components';

type ButtonVariant = 'primary' | 'secondary' | 'outline' | 'text';
type ButtonSize = 'small' | 'medium' | 'large';

interface ButtonProps {
  children: React.ReactNode;
  variant?: ButtonVariant;
  size?: ButtonSize;
  fullWidth?: boolean;
  onClick?: () => void;
  disabled?: boolean;
  type?: 'button' | 'submit' | 'reset';
  className?: string;
}

const getBackgroundColor = (variant: ButtonVariant, theme: any) => {
  switch (variant) {
    case 'primary':
      return theme.primary;
    case 'secondary':
      return theme.secondary;
    case 'outline':
    case 'text':
      return 'transparent';
    default:
      return theme.primary;
  }
};

const getColor = (variant: ButtonVariant, theme: any) => {
  switch (variant) {
    case 'primary':
      return '#ffffff';
    case 'secondary':
      return '#ffffff';
    case 'outline':
      return theme.primary;
    case 'text':
      return theme.primary;
    default:
      return '#ffffff';
  }
};

const getBorder = (variant: ButtonVariant, theme: any) => {
  switch (variant) {
    case 'outline':
      return `2px solid ${theme.primary}`;
    default:
      return 'none';
  }
};

const getPadding = (size: ButtonSize) => {
  switch (size) {
    case 'small':
      return '0.5rem 1rem';
    case 'medium':
      return '0.75rem 1.5rem';
    case 'large':
      return '1rem 2rem';
    default:
      return '0.75rem 1.5rem';
  }
};

const getFontSize = (size: ButtonSize) => {
  switch (size) {
    case 'small':
      return '0.875rem';
    case 'medium':
      return '1rem';
    case 'large':
      return '1.125rem';
    default:
      return '1rem';
  }
};

const StyledButton = styled.button<{
  $variant: ButtonVariant;
  $size: ButtonSize;
  $fullWidth: boolean;
}>`
  background: ${({ theme, $variant }) => getBackgroundColor($variant, theme)};
  color: ${({ theme, $variant }) => getColor($variant, theme)};
  border: ${({ theme, $variant }) => getBorder($variant, theme)};
  border-radius: 8px;
  padding: ${({ $size }) => getPadding($size)};
  font-size: ${({ $size }) => getFontSize($size)};
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: ${({ $fullWidth }) => $fullWidth ? '100%' : 'auto'};
  font-family: 'Poppins', sans-serif;
  
  &:hover:not(:disabled) {
    opacity: 0.9;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }
  
  &:active:not(:disabled) {
    transform: translateY(0);
    box-shadow: none;
  }
  
  &:disabled {
    background: ${({ theme }) => theme.border};
    color: ${({ theme }) => theme.textSecondary};
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
    opacity: 0.7;
  }
  
  svg {
    margin-right: ${({ children }) => children ? '0.5rem' : '0'};
  }
`;

const Button: React.FC<ButtonProps> = ({ 
  children, 
  variant = 'primary', 
  size = 'medium', 
  fullWidth = false,
  onClick,
  disabled = false,
  type = 'button',
  className
}) => {
  return (
    <StyledButton 
      $variant={variant} 
      $size={size} 
      $fullWidth={fullWidth}
      onClick={onClick}
      disabled={disabled}
      type={type}
      className={className}
    >
      {children}
    </StyledButton>
  );
};

export default Button;
