// src/themes.ts

import { DefaultTheme } from 'styled-components';
import { darken } from 'polished'; // Importez lighten et darken

export const lightTheme: DefaultTheme = {
  name: 'light',
  // Couleurs principales
  background: '#f8f9fa',
  surface: '#ffffff',
  primary: '#8A63D2',
  secondary: '#6c757d',
  accent: '#B084CC',
  primaryHover: darken(0.1, '#8A63D2'), // Calculé à partir de primary
  accentHover: darken(0.1, '#B084CC'),  // Calculé à partir de accent

  // Texte
  text: '#212529',
  textSecondary: '#495057',
  textLight: '#FFFFFF',
  textMuted: '#868e96',

  // Éléments d'interface
  border: '#dee2e6',
  cardShadow: '0 4px 12px rgba(0, 0, 0, 0.08)',
  headerShadow: '0 2px 4px rgba(0, 0, 0, 0.05)',
  borderSlight: '#e9ecef',
  shadowSmall: '0 2px 4px rgba(0,0,0,0.05)',

  // Éléments spécifiques
  logoBg: '#8A63D2',
  navActive: '#8A63D2',
  navInactive: '#adb5bd',

  // Arrière-plans spéciaux
  surfaceAlt: '#f1f3f5',
  gradientStart: '#8A63D2',
  gradientEnd: '#B084CC',

  // --- Propriétés pour formulaires et états ---
  inputBackground: '#ffffff',
  errorColor: '#d9534f',
  successColor: '#28a745', // Couleur de succès

  disabledBackground: '#e9ecef', // Fond pour les éléments désactivés
  disabledText: '#adb5bd',       // Texte pour les éléments désactivés
  
  // --- Propriétés spécifiques ajoutées pour les composants ---
  heroOverlay: 'rgba(50, 20, 80, 0.35)',

  // --- Propriétés pour les boutons Google ---
  googleButtonBackground: '#ffffff',
  googleButtonText: '#000000',
  googleButtonBorder: '#e0e0e0',
  googleButtonHoverBackground: '#f0f0f0',

  // --- Propriétés pour les jeux ---
  hoverShadow: '0 10px 40px rgba(0,0,0,0.15)',
  zenTetrisPiece1: '#2ecc71',
  zenTetrisPiece2: '#3498db',
  zenTetrisPiece3: '#9b59b6',
  zenTetrisPiece4: '#f1c40f',
  zenTetrisPiece5: '#e67e22',
  zenTetrisPiece6: '#e74c3c',
  zenTetrisPiece7: '#1abc9c',
  zenTetrisBackgroundCell: '#34495e',
  zenTetrisBoardBackground: '#2c3e50'
};

export const darkTheme: DefaultTheme = {
  name: 'dark', 
  // Couleurs principales
  background: '#1A1A1D',
  surface: '#2C2F33',
  primary: '#1ECB6B',
  secondary: '#99AAB5',
  accent: '#23D5AB',
  primaryHover: darken(0.1, '#1ECB6B'),
  accentHover: darken(0.1, '#23D5AB'),

  // Texte
  text: '#F0F2F5',
  textSecondary: '#B0B8BF',
  textLight: '#FFFFFF',
  textMuted: '#72767D',

  // Éléments d'interface
  border: '#40444B',
  cardShadow: '0 4px 12px rgba(0, 0, 0, 0.25)',
  headerShadow: '0 2px 4px rgba(0, 0, 0, 0.2)',
  borderSlight: '#343a40',
  shadowSmall: '0 2px 4px rgba(0,0,0,0.1)',

  // Éléments spécifiques
  logoBg: '#1ECB6B',
  navActive: '#1ECB6B',
  navInactive: '#72767D',

  // Arrière-plans spéciaux
  surfaceAlt: '#23272A',
  gradientStart: '#1ECB6B',
  gradientEnd: '#23D5AB',

  // --- Propriétés pour formulaires et états ---
  inputBackground: '#23272A',
  errorColor: '#F07178',
  successColor: '#2ecc71', // Couleur de succès

  disabledBackground: '#40444B', // Fond pour les éléments désactivés
  disabledText: '#72767D',       // Texte pour les éléments désactivés

  // --- Propriétés spécifiques ajoutées pour les composants ---
  heroOverlay: 'rgba(0, 0, 0, 0.5)',

  // --- Propriétés pour les boutons Google ---
  googleButtonBackground: '#4285F4', // Bleu de la marque Google
  googleButtonText: '#ffffff',
  googleButtonBorder: '#4285F4',
  googleButtonHoverBackground: '#357ae8',

  // --- Propriétés pour les jeux ---
  hoverShadow: '0 10px 40px rgba(0,0,0,0.25)',
  zenTetrisPiece1: '#2ecc71',
  zenTetrisPiece2: '#3498db',
  zenTetrisPiece3: '#9b59b6',
  zenTetrisPiece4: '#f1c40f',
  zenTetrisPiece5: '#e67e22',
  zenTetrisPiece6: '#e74c3c',
  zenTetrisPiece7: '#1abc9c',
  zenTetrisBackgroundCell: '#34495e',
  zenTetrisBoardBackground: '#2c3e50'
};