// src/components/SessionsList.tsx

import React, { useState, useMemo, useEffect, useContext } from 'react';
import { fetchSessionManifest } from '../data/sessions';
import { SessionManifestEntry } from '../models';
import styled, { css, DefaultTheme } from 'styled-components';
import { Link, useLocation } from 'react-router-dom';
import { FiSearch, FiGrid, FiList, FiX, FiFilter, FiChevronDown, FiLoader } from 'react-icons/fi';
import { useLang, Language } from '../LangProvider';
import { useTranslation } from 'react-i18next';

// --- Styled Components ---

const PageContainer = styled.div`
  padding: 1rem;
  max-width: 1200px;
  margin: 0 auto;
  color: ${({ theme }) => theme.text};
`;

const HeaderControls = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 1.5rem;

  @media (min-width: 768px) {
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
  }
`;

const SearchInputContainer = styled.div`
  display: flex;
  align-items: center;
  background: ${({ theme }) => theme.surfaceAlt};
  border-radius: 25px;
  padding: 0.3rem 0.5rem 0.3rem 1rem;
  flex-grow: 1;
  box-shadow: ${({ theme }) => theme.cardShadow && theme.cardShadow.startsWith('0') ? `inset ${theme.cardShadow}` : 'inset 0 1px 3px rgba(0,0,0,0.06)'};

  svg {
    color: ${({ theme }) => theme.textSecondary};
    margin-right: 0.75rem;
    font-size: 1.2rem;
  }

  input {
    flex-grow: 1;
    border: none;
    background: transparent;
    padding: 0.6rem 0.2rem;
    font-size: 1rem;
    color: ${({ theme }) => theme.text};
    outline: none;

    &::placeholder {
      color: ${({ theme }) => theme.textMuted};
    }
  }
`;

const ClearButton = styled.button`
  background: transparent;
  border: none;
  color: ${({ theme }) => theme.textSecondary};
  cursor: pointer;
  padding: 0.5rem;
  font-size: 1.2rem;
  display: flex;
  align-items: center;

  &:hover {
    color: ${({ theme }) => theme.primary};
  }
`;

const FilterAndDisplayControls = styled.div`
  display: flex;
  gap: 0.75rem;
  align-items: center;

  @media (max-width: 767px) {
    justify-content: space-between;
  }
`;

const FilterDropdownContainer = styled.div`
  position: relative;
`;

const FilterButton = styled.button`
  background: ${({ theme }) => theme.surface};
  color: ${({ theme }) => theme.textSecondary};
  border: 1px solid ${({ theme }) => theme.border};
  border-radius: 20px;
  padding: 0.6rem 1rem;
  font-size: 0.9rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  box-shadow: ${({ theme }) => theme.cardShadow};

  &:hover {
    border-color: ${({ theme }) => theme.primary};
    color: ${({ theme }) => theme.primary};
  }
`;

const FilterPopup = styled.div`
  position: absolute;
  top: calc(100% + 0.5rem);
  left: 0;
  background: ${({ theme }) => theme.surface};
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  padding: 1rem;
  z-index: 10;
  width: 250px;

  h4 {
    margin-top: 0;
    margin-bottom: 0.75rem;
    font-size: 1rem;
    color: ${({ theme }) => theme.primary};
  }

  label {
    display: block;
    margin-bottom: 0.3rem;
    font-size: 0.9rem;
    color: ${({ theme }) => theme.textSecondary};
  }

  select {
    width: 100%;
    padding: 0.6rem;
    border-radius: 6px;
    border: 1px solid ${({ theme }) => theme.border};
    background: ${({ theme }) => theme.inputBackground || theme.surfaceAlt};
    color: ${({ theme }) => theme.text};
    font-size: 0.9rem;
    margin-bottom: 1rem;

    &:last-child {
      margin-bottom: 0;
    }
  }
`;

interface DisplayToggleButtonProps {
  $isActive?: boolean;
}

const DisplayToggleButton = styled.button<DisplayToggleButtonProps>`
  background: ${({ theme, $isActive }) => $isActive ? theme.primary : theme.surface};
  color: ${({ theme, $isActive }) => $isActive ? theme.textLight : theme.textSecondary};
  border: 1px solid ${({ theme, $isActive }) => $isActive ? theme.primary : theme.border};
  border-radius: 50%;
  padding: 0.6rem;
  font-size: 1.2rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: ${({ theme }) => theme.cardShadow};

  &:hover {
    border-color: ${({ theme }) => theme.primary};
    color: ${({ theme, $isActive }) => !$isActive && theme.primary};
  }
`;

// CORRECTION: Utiliser $viewMode dans l'interface et la définition
interface SessionListContainerProps {
  $viewMode: 'grid' | 'list';
}

const SessionListContainer = styled.div<SessionListContainerProps>`
  display: grid;
  gap: 1.5rem;

  ${({ $viewMode }) => $viewMode === 'grid' && css`
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    @media (max-width: 600px) {
      grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    }
  `}

  ${({ $viewMode }) => $viewMode === 'list' && css`
    grid-template-columns: 1fr;
  `}
`;

interface SessionCardProps {
  $viewMode: 'grid' | 'list';
}

const SessionCard = styled(Link)<SessionCardProps>`
  background: ${({ theme }) => theme.surface};
  border-radius: 12px;
  box-shadow: ${({ theme }) => theme.cardShadow};
  color: inherit;
  text-decoration: none;
  overflow: hidden;
  transition: transform 0.2s ease-out, box-shadow 0.2s ease-out;
  display: flex;
  flex-direction: column;

  &:hover {
    transform: translateY(-4px);
    box-shadow: ${({ theme }) => theme.cardHoverShadow || '0 6px 18px rgba(0,0,0,0.1)'};
  }

  ${({ $viewMode }) => $viewMode === 'list' && css`
    flex-direction: row;
    align-items: center;
    padding: 1rem;
    gap: 1rem;
  `}
`;

interface CardImagePlaceholderProps {
  $viewMode: 'grid' | 'list';
  imageUrl?: string | null;
  'data-type-emoji'?: string;
}

const CardImagePlaceholder = styled.div<CardImagePlaceholderProps>`
  background-color: ${({ theme }) => theme.surfaceAlt};
  display: flex;
  align-items: center;
  justify-content: center;
  color: ${({ theme }) => theme.textMuted};
  font-size: 2rem;
  background-image: ${({ imageUrl }) => imageUrl ? `url(${imageUrl})` : 'none'};
  background-size: cover;
  background-position: center;

  ${({ $viewMode }) => $viewMode === 'grid' && css`
    height: 150px;
    border-bottom: 1px solid ${({ theme }) => theme.border};
  `}

  ${({ $viewMode }) => $viewMode === 'list' && css`
    width: 100px;
    height: 100px;
    min-width: 100px;
    border-radius: 8px;
    flex-shrink: 0;
  `}

  &::before {
    content: ${(props) => !props.imageUrl && props['data-type-emoji'] ? `"${props['data-type-emoji']}"` : '""'};
  }

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
  }
`;

interface CardContentProps {
  $viewMode: 'grid' | 'list';
}

const CardContent = styled.div<CardContentProps>`
  padding: 1.2rem;
  flex-grow: 1;
  display: flex;
  flex-direction: column;

  ${({ $viewMode }) => $viewMode === 'list' && css`
    padding: 0;
  `}
`;

const CardTitle = styled.h3`
  margin: 0 0 0.5rem 0;
  color: ${({ theme }) => theme.primary};
  font-size: 1.2rem;
  line-height: 1.3;
`;

const CardMeta = styled.div`
  font-size: 0.8rem;
  color: ${({ theme }) => theme.textMuted};
  margin-bottom: 0.5rem;

  span {
    margin-right: 0.8rem;
    &:last-child {
      margin-right: 0;
    }
  }
`;

const CardTags = styled.div`
  font-size: 0.8rem;
  color: ${({ theme }) => theme.accent};
  margin-top: 0.5rem;

  span {
    background-color: ${({ theme }) => `${theme.accent}20`};
    padding: 0.2rem 0.5rem;
    border-radius: 10px;
    margin-right: 0.4rem;
    margin-bottom: 0.4rem;
    display: inline-block;
  }
`;

const NoResultsMessage = styled.div`
  text-align: center;
  padding: 2rem;
  color: ${({ theme }) => theme.textSecondary};
  font-style: italic;
`;

const LoadingContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 3rem;
  font-size: 1.2rem;
  color: ${({ theme }) => theme.primary};

  svg {
    margin-right: 0.5rem;
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;

const ErrorMessage = styled.p`
  color: ${({ theme }) => theme.errorColor || 'red'};
  text-align: center;
  padding: 2rem;
`;

const getTypeEmoji = (type: string) => {
  switch (type.toLowerCase()) {
    case 'hypnosis': return '🌀';
    case 'silence': return '🙊';
    case 'affirmation': return '🪁';
    case 'meditation': return '🧘';
    case 'training': return '🏋️';
    case 'story': return '📖';
    case 'custom': return '✨';
    default: return '💡';
  }
};

const SessionsList: React.FC = () => {
  const location = useLocation();
  const { lang } = useLang();
  const { t } = useTranslation();

  const queryParams = new URLSearchParams(location.search);
  const initialType = queryParams.get('type') || '';

  const [allSessionMetadata, setAllSessionMetadata] = useState<SessionManifestEntry[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const [search, setSearch] = useState('');
  const [type, setType] = useState(initialType);
  const [duration, setDuration] = useState('');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [showFilters, setShowFilters] = useState(false);

  useEffect(() => {
    setType(initialType);
  }, [initialType]);

  useEffect(() => {
    const loadManifest = async () => {
      if (!lang) return;
      setIsLoading(true);
      setError(null);
      try {
        const manifestData = await fetchSessionManifest(lang);
        setAllSessionMetadata(manifestData);
      } catch (err) {
        console.error("SessionsList: Error loading session manifest:", err);
        setError(err instanceof Error ? err.message : t('errors.cantLoadList', "Impossible de charger la liste des sessions."));
      } finally {
        setIsLoading(false);
      }
    };
    loadManifest();
  }, [lang, t]);

  const filteredSessions = useMemo(() => {
    return allSessionMetadata.filter((s) => {
      const searchLower = search.toLowerCase();
      const matchesSearch =
        s.title.toLowerCase().includes(searchLower) ||
        (s.tags || []).some((tagItem) => tagItem.toLowerCase().includes(searchLower));

      const matchesType = type ? s.type === type : true;

      const sessionDuration = s.estimatedDuration || 0;
      const matchesDuration = duration ?
        (duration === "0-15" && sessionDuration <= 15) ||
        (duration === "15-30" && sessionDuration > 15 && sessionDuration <= 30) ||
        (duration === "30+" && sessionDuration > 30)
        : true;

      return matchesSearch && matchesType && matchesDuration;
    });
  }, [allSessionMetadata, search, type, duration]);

  const uniqueSessionTypes = useMemo(() => {
    if (!allSessionMetadata.length) return [];
    const types = new Set(allSessionMetadata.map(s => s.type));
    return Array.from(types).map(tVal => ({ value: tVal, label: t(`sessionTypes.${tVal}`, tVal.charAt(0).toUpperCase() + tVal.slice(1)) }));
  }, [allSessionMetadata, t]);

  if (isLoading) {
    return <LoadingContainer><FiLoader /> {t('loading.sessions', 'Chargement des sessions...')}</LoadingContainer>;
  }

  if (error) {
    return <ErrorMessage>{error}</ErrorMessage>;
  }

  return (
    <PageContainer>
      <HeaderControls>
        <SearchInputContainer>
          <FiSearch />
          <input
            type="text"
            placeholder={t('sessions.searchPlaceholder', "Rechercher une séance...")}
            value={search}
            onChange={(e) => setSearch(e.target.value)}
          />
          {search && <ClearButton onClick={() => setSearch('')}><FiX /></ClearButton>}
        </SearchInputContainer>
        <FilterAndDisplayControls>
          <FilterDropdownContainer>
            <FilterButton onClick={() => setShowFilters(!showFilters)}>
              <FiFilter /> {t('sessions.filters', 'Filtres')} <FiChevronDown style={{ transform: showFilters ? 'rotate(180deg)' : 'rotate(0deg)', transition: 'transform 0.2s'}}/>
            </FilterButton>
            {showFilters && (
              <FilterPopup>
                <h4>{t('sessions.filterBy', 'Filtrer par')}</h4>
                <label htmlFor="sessionType">{t('sessions.sessionType', 'Type de séance')}</label>
                <select id="sessionType" value={type} onChange={(e) => setType(e.target.value)}>
                  <option value="">{t('sessions.allTypes', 'Tous types')}</option>
                  {uniqueSessionTypes.map(tOption => <option key={tOption.value} value={tOption.value}>{getTypeEmoji(tOption.value)} {tOption.label}</option>)}
                </select>

                <label htmlFor="sessionDuration">{t('sessions.duration.label', 'Durée')}</label>
                <select id="sessionDuration" value={duration} onChange={(e) => setDuration(e.target.value)}>
                  <option value="">{t('sessions.allDurations', 'Toutes durées')}</option>
                  <option value="0-15">{t('sessions.duration.under15', 'Moins de 15 min')}</option>
                  <option value="15-30">{t('sessions.duration.15to30', '15 - 30 min')}</option>
                  <option value="30+">{t('sessions.duration.over30', 'Plus de 30 min')}</option>
                </select>
              </FilterPopup>
            )}
          </FilterDropdownContainer>
          <DisplayToggleButton $isActive={viewMode === 'grid'} onClick={() => setViewMode('grid')} title={t('sessions.gridView', "Vue Grille")}>
            <FiGrid />
          </DisplayToggleButton>
          <DisplayToggleButton $isActive={viewMode === 'list'} onClick={() => setViewMode('list')} title={t('sessions.listView', "Vue Liste")}>
            <FiList />
          </DisplayToggleButton>
        </FilterAndDisplayControls>
      </HeaderControls>

      {filteredSessions.length > 0 ? (
        // CORRECTION: Utiliser $viewMode pour SessionListContainer
        <SessionListContainer $viewMode={viewMode}>
          {filteredSessions.map((s: SessionManifestEntry) => {
            return (
              <SessionCard to={`/sessions/${s.id}`} key={s.id} $viewMode={viewMode}>
                <CardImagePlaceholder
                  $viewMode={viewMode}
                  imageUrl={s.imageUrl}
                  data-type-emoji={!s.imageUrl ? getTypeEmoji(s.type) : undefined}
                />
                <CardContent $viewMode={viewMode}>
                  <CardTitle>{s.title}</CardTitle>
                  <CardMeta>
                    <span>{t('sessions.durationLabel', 'Durée')}: {s.estimatedDuration || 'N/A'} {t('units.min', 'min')}</span>
                  </CardMeta>
                  {(s.tags && s.tags.length > 0) && (
                    <CardTags>
                      {(s.tags || []).slice(0, viewMode === 'list' ? 2 : 3).map((tag: string) => <span key={tag}>#{tag}</span>)}
                    </CardTags>
                  )}
                </CardContent>
              </SessionCard>
            );
          })}
        </SessionListContainer>
      ) : (
        <NoResultsMessage>
            {search || type || duration
                ? t('sessions.noResultsMatchCriteria', "Aucune séance ne correspond à vos critères.")
                : t('sessions.noSessionsAvailable', "Aucune session disponible pour le moment.")
            }
        </NoResultsMessage>
      )}
    </PageContainer>
  );
};

export default SessionsList;