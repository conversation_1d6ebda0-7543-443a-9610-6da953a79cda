// src/pages/JournalPage.tsx

import React, { useEffect, useState, useMemo, useContext } from 'react';
import styled, { ThemeContext, DefaultTheme } from 'styled-components';
import { Link } from 'react-router-dom';
import { fetchSessionManifest } from '../data/sessions';
import { SessionManifestEntry } from '../models';
import { useLang } from '../LangProvider';
import { useTranslation } from 'react-i18next';
import { FiBookOpen, FiLoader, FiChevronRight } from 'react-icons/fi';

// --- Styled Components ---

const PageContainer = styled.div`
  padding: 1.5rem;
  max-width: 800px; // Un peu plus resserré pour un journal
  margin: 0 auto;
  color: ${({ theme }) => theme.text};
`;

const PageHeader = styled.div`
  text-align: center;
  margin-bottom: 3rem; // Plus d'espace
  h1 {
    font-size: 2.4rem; // Légèrement plus grand
    color: ${({ theme }) => theme.primary};
    margin-bottom: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    svg {
      font-size: 2.2rem; // Taille de l'icône
      opacity: 0.8;
    }
  }
  p {
    font-size: 1.05rem; // Légèrement plus grand
    color: ${({ theme }) => theme.textSecondary};
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
  }
`;

const SessionNotesCard = styled.div`
  background: ${({ theme }) => theme.surface};
  border-radius: 16px; // Plus arrondi
  box-shadow: ${({ theme }) => theme.cardShadow};
  margin-bottom: 2.5rem; // Plus d'espace entre les cartes
  overflow: hidden;
  transition: box-shadow 0.3s ease;

  &:hover {
    box-shadow: 0 8px 25px rgba(0,0,0,0.1); // Ombre plus prononcée au survol
  }
`;

const SessionHeader = styled(Link)`
  display: flex; // Pour aligner titre et icône/compteur
  justify-content: space-between;
  align-items: center;
  background: ${({ theme }) => theme.surfaceAlt};
  padding: 1.2rem 1.5rem; // Padding ajusté
  border-bottom: 1px solid ${({ theme }) => theme.border};
  text-decoration: none;
  transition: background-color 0.2s ease;

  &:hover {
    background: ${({ theme }) => theme.primary}1A; 
  }

  h2 {
    margin: 0;
    font-size: 1.5rem; // Ajusté
    color: ${({ theme }) => theme.primary};
    font-weight: 600;
  }

  span { // Pour le compteur de notes ou l'icône de flèche
    display: flex;
    align-items: center;
    font-size: 0.9rem;
    color: ${({ theme }) => theme.textMuted};
    svg {
      margin-left: 0.3rem;
      font-size: 1.2rem;
    }
  }
`;

const NotesList = styled.div`
  padding: 1.5rem; // Padding uniforme
  display: flex;
  flex-direction: column;
  gap: 1rem; // Espace entre les notes
`;

const NoteEntry = styled.div`
  background: ${({ theme }) => theme.background};
  border-radius: 8px;
  padding: 1rem 1.2rem; // Padding ajusté
  border-left: 4px solid ${({ theme }) => theme.accent}; // Accent coloré
  font-size: 1rem; // Taille de police augmentée
  color: ${({ theme }) => theme.textSecondary};
  line-height: 1.7;
  box-shadow: 0 1px 3px rgba(0,0,0,0.04);
  white-space: pre-wrap; 
`;

const NoNotesMessage = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 1rem; // Plus de padding
  text-align: center;
  color: ${({ theme }) => theme.textSecondary};
  background-color: ${({ theme }) => theme.surfaceAlt};
  border-radius: 16px;
  border: 2px dashed ${({ theme }) => theme.border}; // Bordure en pointillés

  svg {
    font-size: 3.5rem; // Icône plus grande
    margin-bottom: 1.5rem;
    color: ${({ theme }) => theme.textMuted};
    opacity: 0.7;
  }
  
  p {
    font-size: 1.2rem; // Texte plus grand
    margin-bottom: 0.75rem;
    font-weight: 500;
    color: ${({ theme }) => theme.text};
  }
  
  span {
    font-size: 0.95rem;
    max-width: 400px;
    line-height: 1.5;
  }
`;

const LoadingContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  text-align: center;
  color: ${({ theme }) => theme.primary};
  min-height: 60vh; // Pour occuper plus d'espace
  svg { font-size: 3rem; margin-bottom: 1rem; animation: spin 1s linear infinite; }
  @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
`;

const ErrorMessage = styled.div` // Changé en div pour potentiellement inclure un lien
  color: ${({ theme }) => theme.errorColor || 'red'};
  text-align: center;
  padding: 2rem;
  background-color: ${({ theme }) => theme.surfaceAlt};
  border-radius: 8px;
  border: 1px solid ${({ theme }) => theme.errorColor || 'red'}33;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;

  a {
    color: ${({ theme }) => theme.primary};
    text-decoration: underline;
  }
`;

const getAllJournalEntries = (): Record<string, string[]> => {
  const raw = localStorage.getItem('piknowkyo_journal');
  try {
    if (raw) {
      const parsed = JSON.parse(raw);
      // S'assurer que c'est bien un objet de tableaux de chaînes
      if (typeof parsed === 'object' && parsed !== null) {
        for (const key in parsed) {
          if (!Array.isArray(parsed[key]) || !parsed[key].every((item: any) => typeof item === 'string')) {
            console.warn("Format incorrect dans localStorage pour piknowkyo_journal, clé:", key);
            delete parsed[key]; // Supprimer les entrées malformées
          }
        }
        return parsed;
      }
    }
  } catch (error) {
    console.error("Erreur de parsing du journal depuis localStorage:", error);
  }
  return {};
};

const JournalPage: React.FC = () => {
  const { t } = useTranslation();
  const { lang } = useLang();

  const [journalEntries, setJournalEntries] = useState<Record<string, string[]>>(getAllJournalEntries());
  const [allSessionMetadata, setAllSessionMetadata] = useState<SessionManifestEntry[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const theme = useContext(ThemeContext) as DefaultTheme;

  useEffect(() => {
    const loadSessionMetadata = async () => {
      setIsLoading(true);
      setError(null);
      try {
        const manifestData = await fetchSessionManifest(lang);
        setAllSessionMetadata(manifestData);
      } catch (err) {
        console.error("Error loading session manifest for journal:", err);
        setError(err instanceof Error ? err.message : t('errors.cantLoadSessions', "Impossible de charger les données des sessions."));
      } finally {
        setIsLoading(false);
      }
    };
    loadSessionMetadata();
  }, [lang, t]);

  useEffect(() => {
    // Mettre à jour les entrées du journal si elles changent (par exemple, après avoir ajouté une note)
    // Cela pourrait être déclenché par un événement personnalisé ou un re-render forcé si nécessaire.
    // Pour l'instant, on le recharge au montage.
    const handleStorageChange = () => {
        setJournalEntries(getAllJournalEntries());
    };
    window.addEventListener('storage', handleStorageChange); // Écouter les changements de localStorage d'autres onglets
    setJournalEntries(getAllJournalEntries()); // Chargement initial

    return () => {
        window.removeEventListener('storage', handleStorageChange);
    };
  }, []); 

  const sessionIdsWithNotes = useMemo(() => 
    Object.keys(journalEntries).filter(sessionId => journalEntries[sessionId]?.length > 0), 
    [journalEntries]
  );

  if (isLoading) {
    return <LoadingContainer><FiLoader /> {t('loading.journal', 'Chargement du journal...')}</LoadingContainer>;
  }

  if (error) {
    return <ErrorMessage><p>{error}</p> <Link to="/">{t('actions.backToHome', "Retour à l'accueil")}</Link></ErrorMessage>;
  }

  return (
    <PageContainer>
      <PageHeader>
        <h1><FiBookOpen /> {t('journal.title', 'Journal de Suivi')}</h1>
        <p>{t('journal.description', 'Retrouvez ici toutes vos notes personnelles, classées par séance. Réfléchissez à vos expériences et suivez votre progression.')}</p>
      </PageHeader>

      {sessionIdsWithNotes.length === 0 && (
        <NoNotesMessage>
          <FiBookOpen />
          <p>{t('journal.noNotesYet', 'Votre journal est encore vide.')}</p>
          <span>{t('journal.startSessionPrompt', 'Commencez une séance et prenez des notes pour voir vos réflexions ici.')}</span>
        </NoNotesMessage>
      )}

      {sessionIdsWithNotes.map((sessionId) => {
        const sessionMetaData = allSessionMetadata.find(s => s.id === sessionId);
        const sessionTitle = sessionMetaData?.title || t('journal.unknownSession', 'Séance (ID: {{id}})', { id: sessionId });
        const entriesForSession = journalEntries[sessionId];

        return (
          <SessionNotesCard key={sessionId}>
            <SessionHeader to={`/sessions/${sessionId}`}>
              <h2>{sessionTitle}</h2>
              <span>
                {entriesForSession.length} {entriesForSession.length > 1 ? t('journal.notesPlural', 'notes') : t('journal.noteSingular', 'note')}
                <FiChevronRight />
              </span>
            </SessionHeader>
            <NotesList>
              {/* Afficher un aperçu des X dernières notes, par exemple */}
              {entriesForSession.slice(0, 3).map((note: string, index: number) => (
                <NoteEntry key={index}>
                  {note.length > 150 ? `${note.substring(0, 150)}...` : note} 
                  {/* Tronquer les notes longues pour l'aperçu */}
                </NoteEntry>
              ))}
              {entriesForSession.length > 3 && (
                <NoteEntry style={{ textAlign: 'center', fontStyle: 'italic', background: 'transparent', border: 'none', boxShadow: 'none' }}>
                  <Link to={`/sessions/${sessionId}#journal`} style={{color: theme.primary}}>
                    {t('journal.seeAllNotes', 'Voir toutes les {{count}} notes...', {count: entriesForSession.length})}
                  </Link>
                </NoteEntry>
              )}
            </NotesList>
          </SessionNotesCard>
        );
      })}
    </PageContainer>
  );
};

export default JournalPage;