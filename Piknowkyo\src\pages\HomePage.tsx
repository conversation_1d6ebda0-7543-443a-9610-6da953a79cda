// src/pages/HomePage.tsx

import React, { useContext } from 'react';
import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import styled, { ThemeContext, DefaultTheme } from 'styled-components'; // Gardez ThemeContext de styled-components pour les styled-components
import {
  FiArrowRight, FiHeart, FiBookOpen, FiSettings, FiSmile, FiStar, FiMic
} from 'react-icons/fi';

import heroBgLight from '../assets/images/hero-background_light.webp';
import heroBgDark from '../assets/images/hero-background_dark.webp';

// --- Importer votre useTheme personnalisé ---
import { useTheme } from '../ThemeProvider'; // Assurez-vous que ce chemin est correct : ../ThemeProvider si HomePage est dans pages/ et ThemeProvider dans src/

const PageContainer = styled.div`
  padding: 0 1rem 2rem 1rem;
  max-width: 1200px;
  margin: 0 auto;
  color: ${({ theme }) => theme.text};
`;

interface HeroSectionProps {
  $bgimage?: string;
}

const HeroSection = styled.section<HeroSectionProps>`
  text-align: center;
  padding: 3rem 1rem;
  min-height: 40vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  background-image: ${({ $bgimage, theme }: { $bgimage?: string; theme: DefaultTheme }) =>
    $bgimage
      ? `linear-gradient(rgba(0,0,0,0.35), rgba(0,0,0,0.35)), url(${$bgimage})`
      : `linear-gradient(135deg, ${theme.gradientStart || theme.primary}, ${theme.gradientEnd || theme.accent})`
  };

  background-size: cover;
  background-position: center center;
  background-repeat: no-repeat;

  color: ${({ theme }) => theme.textLight || '#fff'};
  box-shadow: ${({ theme }) => theme.headerShadow || '0 4px 12px rgba(0,0,0,0.1)'};
  position: relative;
  margin: 0 -1rem 2.5rem -1rem;

  h1, p, a {
    position: relative;
    z-index: 2;
  }

  h1 {
    font-size: 2.2rem;
    margin-bottom: 0.5rem;
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0,0,0,0.4);
  }
  p {
    font-size: 1.05rem;
    max-width: 600px;
    margin: 0 auto 1.5rem auto;
    line-height: 1.6;
    text-shadow: 0 1px 3px rgba(0,0,0,0.3);
  }
`;

const PrimaryButton = styled(Link)`
  display: inline-flex;
  align-items: center;
  background: ${({ theme }) => theme.surface || '#fff'};
  color: ${({ theme }) => theme.primary};
  padding: 0.7rem 1.5rem;
  border-radius: 25px;
  text-decoration: none;
  font-weight: 600;
  font-size: 1rem;
  box-shadow: 0 2px 8px rgba(0,0,0,0.2);
  transition: transform 0.2s ease-out, box-shadow 0.2s ease-out;
  position: relative;
  z-index: 2;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.25);
  }

  svg {
    margin-left: 0.5rem;
  }
`;

const Section = styled.section`
  margin-bottom: 2.5rem;
  padding: 0 0.5rem;

  h2 {
    font-size: 1.6rem;
    color: ${({ theme }) => theme.primary};
    margin-bottom: 1.2rem;
    text-align: center;
  }
`;

const QuickAccessGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
`;

const QuickAccessCard = styled(Link)`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  background: ${({ theme }) => theme.surfaceAlt};
  border-radius: 12px;
  box-shadow: ${({ theme }) => theme.cardShadow};
  padding: 1.5rem 1rem;
  color: ${({ theme }) => theme.text};
  text-decoration: none;
  transition: transform 0.2s ease-out, box-shadow 0.2s ease-out;
  min-height: 120px;

  &:hover {
    transform: translateY(-3px);
    box-shadow: ${({ theme }) => theme.cardHoverShadow || '0 5px 15px rgba(0,0,0,0.08)'};
  }

  svg {
    font-size: 2rem;
    margin-bottom: 0.8rem;
    color: ${({ theme }) => theme.primary};
  }

  span {
    font-weight: 500;
    font-size: 0.95rem;
  }
`;

const WelcomeText = styled.p`
  font-size: 1.1rem;
  text-align: center;
  margin-bottom: 2rem;
  color: ${({ theme }) => theme.textSecondary};
  line-height: 1.6;
`;

const HomePage: React.FC = () => {
  const { t } = useTranslation();
  // --- Utiliser votre useTheme personnalisé pour obtenir l'état darkMode ---
  const { darkMode } = useTheme(); // Ceci vient de votre ThemeProvider.tsx
  // Le ThemeContext de styled-components n'est pas nécessaire ici pour la détection du mode sombre
  // mais il est injecté automatiquement aux styled-components eux-mêmes.

  const heroImageUrl = darkMode ? heroBgDark : heroBgLight;

  const sessionTypes = [
    { to: "/sessions?type=meditation", icon: <FiHeart />, label: t('sessions.meditation') },
    { to: "/sessions?type=hypnose", icon: <FiSmile />, label: t('sessions.hypnosis') },
    { to: "/sessions?type=affirmation", icon: <FiStar />, label: t('sessions.affirmations') },
    { to: "/sessions?type=custom", icon: <FiMic />, label: t('sessions.custom') },
  ];

  // Obtenez le thème de styled-components pour les styles en ligne si nécessaire (comme pour le Link)
  const currentTheme = useContext(ThemeContext) as DefaultTheme;


  return (
    <PageContainer>
      <HeroSection $bgimage={heroImageUrl}>
        <h1>{t('home.title')}</h1>
        <p>
          {t('home.subtitle')}
        </p>
        <PrimaryButton to="/sessions">
          {t('home.exploreButton')} <FiArrowRight />
        </PrimaryButton>
      </HeroSection>

      <Section>
        <WelcomeText>
          {t('home.welcomeText')}
        </WelcomeText>
      </Section>

      <Section>
        <h2>{t('home.quickAccess')}</h2>
        <QuickAccessGrid>
          {sessionTypes.map(session => (
            <QuickAccessCard key={session.to} to={session.to}>
              {session.icon}
              <span>{session.label}</span>
            </QuickAccessCard>
          ))}
          <QuickAccessCard to="/journal">
            <FiBookOpen />
            <span>{t('navigation.journal')}</span>
          </QuickAccessCard>
          <QuickAccessCard to="/settings/audio-assets">
            <FiSettings />
            <span>{t('settings.audio')}</span>
          </QuickAccessCard>
        </QuickAccessGrid>
      </Section>

      <Section style={{ textAlign: 'center', marginTop: '3rem' }}>
        <Link to="/about" style={{ color: currentTheme.primary, textDecoration: 'underline' }}>
          {t('home.learnMore')}
        </Link>
      </Section>
    </PageContainer>
  );
};

export default HomePage;