import styled from 'styled-components';
import { lighten, darken } from 'polished'; // Pour les calculs de couleurs

// Conteneur principal de la page d'authentification
export const AuthPageContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background-color: ${({ theme }) => theme.background};
  transition: background-color 0.3s ease;
  padding: 20px;
  box-sizing: border-box;
`;

// Carte contenant les formulaires d'authentification
export const AuthCard = styled.div`
  background-color: ${({ theme }) => theme.surface};
  border-radius: 12px;
  box-shadow: ${({ theme }) => theme.cardShadow};
  padding: 40px;
  width: 100%;
  max-width: 420px;
  text-align: center;
  transition: background-color 0.3s ease, box-shadow 0.3s ease;
  position: relative;
`;

// Actions d'en-tête (pour le bouton de thème)
export const HeaderActions = styled.div`
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 10;
`;

// Bouton de bascule de thème
export const ThemeToggleButton = styled.button`
  background: none;
  border: none;
  color: ${({ theme }) => theme.text};
  cursor: pointer;
  font-size: 1.2rem;
  padding: 6px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.3s ease, color 0.3s ease;

  &:hover {
    background-color: ${({ theme }) => theme.name === 'light' ? 'rgba(0, 0, 0, 0.05)' : 'rgba(255, 255, 255, 0.1)'};
  }
`;

// Logo de l'application
export const AppLogo = styled.img`
  width: 150px;
  height: auto;
  margin-bottom: 25px;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
`;

// Titre principal de la page
export const Title = styled.h1`
  color: ${({ theme }) => theme.text};
  margin-bottom: 10px;
  font-size: 2.5em;
  font-weight: bold;
  letter-spacing: -0.8px;
`;

// Sous-titre
export const Subtitle = styled.p`
  color: ${({ theme }) => theme.text};
  margin-bottom: 30px;
  font-size: 1.2em;
  opacity: 0.8;
`;

// Formulaire générique
export const Form = styled.form`
  width: 100%;
`;

// Champ de saisie
export const Input = styled.input`
  width: calc(100% - 24px); /* Full width minus padding */
  padding: 12px;
  margin-bottom: 20px;
  border: 1px solid ${({ theme }) => theme.border};
  border-radius: 8px;
  font-size: 1em;
  color: ${({ theme }) => theme.text};
  background-color: ${({ theme }) => theme.inputBackground || theme.surface};
  transition: border-color 0.2s ease, background-color 0.2s ease, box-shadow 0.2s ease;

  &::placeholder {
    color: ${({ theme }) => theme.textSecondary};
    opacity: 0.7;
  }

  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.primary};
    box-shadow: 0 0 0 3px ${({ theme }) => theme.primary}40;
  }

  &:disabled {
    background-color: ${({ theme }) => theme.disabledBackground};
    color: ${({ theme }) => theme.disabledText};
    cursor: not-allowed;
  }
`;

// Bouton principal
export const Button = styled.button`
  width: 100%;
  padding: 12px;
  border: none;
  border-radius: 8px;
  background-color: ${({ theme }) => theme.primary};
  color: ${({ theme }) => theme.textLight};
  font-size: 1.1em;
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.3s ease, transform 0.1s ease;
  margin-top: 10px;

  &:hover {
    background-color: ${({ theme }) => theme.primaryHover || darken(0.1, theme.primary)};
    transform: translateY(-1px);
  }

  &:disabled {
    background-color: ${({ theme }) => theme.disabledBackground};
    color: ${({ theme }) => theme.disabledText};
    cursor: not-allowed;
    transform: none;
  }
`;

// Séparateur "OU"
export const Separator = styled.div`
  display: flex;
  align-items: center;
  text-align: center;
  margin: 30px 0;
  color: ${({ theme }) => theme.textMuted || theme.textSecondary};
  opacity: 0.6;
  font-size: 0.9em;

  &::before,
  &::after {
    content: '';
    flex: 1;
    border-bottom: 1px solid ${({ theme }) => theme.border};
  }

  &:not(:empty)::before {
    margin-right: .75em;
  }

  &:not(:empty)::after {
    margin-left: .75em;
  }
`;

// Bouton Google
export const GoogleButton = styled.button`
  width: 100%;
  padding: 12px;
  border: 1px solid ${({ theme }) => theme.googleButtonBorder || theme.border};
  border-radius: 8px;
  background-color: ${({ theme }) => theme.googleButtonBackground || theme.surface};
  color: ${({ theme }) => theme.googleButtonText || theme.text};
  font-size: 1.1em;
  font-weight: bold;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  transition: background-color 0.3s ease, border-color 0.3s ease, transform 0.1s ease;

  &:hover {
    background-color: ${({ theme }) => theme.googleButtonHoverBackground || darken(0.05, theme.googleButtonBackground || theme.surface)};
    transform: translateY(-1px);
  }

  &:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
    background-color: ${({ theme }) => theme.disabledBackground};
    color: ${({ theme }) => theme.disabledText};
  }
`;

// Messages d'état (erreur, succès, chargement)
export const Message = styled.p<{ type: 'error' | 'success' | 'loading' }>`
  margin-top: 15px;
  font-size: 0.95em;
  border-radius: 8px;
  padding: 10px;
  border: 1px solid;

  ${({ type, theme }) => {
    switch (type) {
      case 'error':
        return `
          color: ${theme.errorColor};
          background-color: ${lighten(0.4, theme.errorColor || '#d9534f')};
          border-color: ${theme.errorColor};
        `;
      case 'success':
        return `
          color: ${theme.successColor};
          background-color: ${lighten(0.4, theme.successColor || '#28a745')};
          border-color: ${theme.successColor};
        `;
      case 'loading':
        return `
          color: ${theme.primary};
          font-weight: bold;
          border: none;
          background: none;
        `;
      default:
        return '';
    }
  }}
`;

// Liens de bascule entre connexion/inscription
export const ToggleLink = styled.button`
  background: none;
  border: none;
  color: ${({ theme }) => theme.primary};
  font-size: 1em;
  margin-top: 20px;
  cursor: pointer;
  text-decoration: underline;
  transition: color 0.2s ease;

  &:hover {
    color: ${({ theme }) => theme.primaryHover || darken(0.1, theme.primary)};
  }
`;