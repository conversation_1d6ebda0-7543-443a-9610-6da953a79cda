// src/pages/MonetizationPage.tsx

import React, { useState, useEffect, useContext } from 'react';
import styled, { css, ThemeContext, DefaultTheme } from 'styled-components';
import { useLang } from '../LangProvider';
import { useTranslation } from 'react-i18next';
import { Link, useNavigate } from 'react-router-dom';
// import { useAuth } from '../AuthContext'; // Supposons un AuthContext
// import { db } from '../services/firebase'; // Votre instance Firestore
// import { doc, getDoc, setDoc, updateDoc, onSnapshot, Timestamp } from "firebase/firestore";
// import { functions, httpsCallable } from '../services/firebase'; // Pour les fonctions Cloud (paiements)
import {
  FiStar, FiZap, FiGift, FiCheckCircle, FiXCircle, FiDollarSign, FiRepeat, FiFileText, FiSettings, FiHeart, FiLoader
} from 'react-icons/fi';

// --- Configuration des Prix (à externaliser plus tard dans Firebase Config) ---
const PREMIUM_PRICE_USD_MONTHLY = 9;
const PREMIUM_CURRENCY = "$";

// --- Types ---
interface CurrentUser { // Placeholder
  uid: string;
  email?: string | null;
  isPremium?: boolean;
  subscriptionEnds?: any; // Sera un Timestamp Firebase
  // ... autres données utilisateur
}

interface SubscriptionDetails { // Pour les détails de l'abonnement
    status: 'active' | 'canceled' | 'past_due' | 'trialing' | null;
    current_period_end: number | null; // Timestamp en secondes
    cancel_at_period_end: boolean;
    // ... autres champs de Stripe/Paddle via Firebase
}


// --- Styled Components ---
const PageContainer = styled.div`
  padding: 1.5rem;
  max-width: 800px;
  margin: 0 auto;
  color: ${({ theme }) => theme.text};
`;

const PageHeader = styled.div`
  text-align: center;
  margin-bottom: 2.5rem;
  h1 {
    font-size: 2.4rem;
    color: ${({ theme }) => theme.primary};
    margin-bottom: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
  }
  p {
    font-size: 1rem;
    color: ${({ theme }) => theme.textSecondary};
    line-height: 1.6;
  }
`;

const PlansContainer = styled.div`
  display: grid;
  grid-template-columns: 1fr; /* Une colonne sur mobile */
  gap: 2rem;
  margin-bottom: 3rem;

  @media (min-width: 768px) {
    grid-template-columns: repeat(2, 1fr); /* Deux colonnes sur desktop */
  }
`;

const PlanCard = styled.div<{ $isCurrentPlan?: boolean, $isPremium?: boolean }>`
  background: ${({ theme }) => theme.surface};
  border-radius: 16px;
  box-shadow: ${({ theme }) => theme.cardShadow};
  padding: 2rem;
  display: flex;
  flex-direction: column;
  border: 2px solid transparent;
  transition: border-color 0.3s ease, transform 0.3s ease;

  ${({ $isPremium, theme }) => $isPremium && css`
    border-color: ${theme.primary}; // Bordure spéciale pour le plan premium
  `}

  ${({ $isCurrentPlan, theme }) => $isCurrentPlan && css`
    // Styles pour le plan actuel de l'utilisateur (si différent du plan affiché)
    // Par exemple, une bordure plus épaisse ou une icône
    // border: 3px solid ${theme.accent};
    // transform: scale(1.02);
  `}

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
  }
`;

const PlanHeader = styled.div`
  text-align: center;
  margin-bottom: 1.5rem;
  border-bottom: 1px solid ${({ theme }) => theme.border};
  padding-bottom: 1.5rem;

  .plan-icon {
    font-size: 3rem;
    color: ${({ theme }) => theme.primary};
    margin-bottom: 0.75rem;
  }
  h3 {
    font-size: 1.8rem;
    color: ${({ theme }) => theme.text};
    margin: 0 0 0.5rem 0;
  }
  .price {
    font-size: 2.2rem;
    font-weight: 700;
    color: ${({ theme }) => theme.primary};
    margin-bottom: 0.25rem;
    span {
      font-size: 1rem;
      font-weight: 400;
      color: ${({ theme }) => theme.textSecondary};
    }
  }
  .billing-cycle {
    font-size: 0.9rem;
    color: ${({ theme }) => theme.textMuted};
  }
`;

const FeatureList = styled.ul`
  list-style: none;
  padding: 0;
  margin-bottom: 2rem;
  flex-grow: 1; /* Pour que le bouton soit en bas */

  li {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 0.8rem;
    font-size: 0.95rem;
    color: ${({ theme }) => theme.textSecondary};
    svg {
      font-size: 1.2rem;
      color: ${({ theme }) => theme.primary};
      flex-shrink: 0;
    }
    &.disabled-feature svg {
      color: ${({ theme }) => theme.textMuted};
      opacity: 0.6;
    }
    &.disabled-feature {
      text-decoration: line-through;
      color: ${({ theme }) => theme.textMuted};
       opacity: 0.7;
    }
  }
`;

const Button = styled.button` /* ... (style de bouton comme dans SettingsPage) ... */ `;

const DonationCard = styled(PlanCard)`
  border-color: ${({ theme }) => theme.accent}; // Couleur d'accent pour le don
  .plan-icon { color: ${({ theme }) => theme.accent}; }
  .price { color: ${({ theme }) => theme.accent}; }
`;

const CurrentSubscriptionInfo = styled.div`
  background: ${({ theme }) => theme.surfaceAlt};
  padding: 1.5rem;
  border-radius: 12px;
  margin-bottom: 2rem;
  text-align: center;
  border: 1px solid ${({ theme }) => theme.border};

  h4 {
    margin-top: 0;
    margin-bottom: 0.75rem;
    color: ${({ theme }) => theme.primary};
  }
  p {
    margin-bottom: 0.5rem;
    color: ${({ theme }) => theme.textSecondary};
  }
  .renewal-date {
    font-weight: 500;
    color: ${({ theme }) => theme.text};
  }
`;

const LegalLinks = styled.div`
  text-align: center;
  margin-top: 3rem;
  font-size: 0.85rem;
  color: ${({ theme }) => theme.textMuted};
  a {
    color: ${({ theme }) => theme.textSecondary};
    text-decoration: underline;
    margin: 0 0.5rem;
    &:hover {
      color: ${({ theme }) => theme.primary};
    }
  }
`;

const LoadingContainer = styled.div` /* ... (comme avant) ... */ `;
const ErrorMessage = styled.div` /* ... (comme avant) ... */ `;

// --- Fonctions Placeholder pour Firebase ---
// const createCheckoutSession = httpsCallable(functions, 'stripeCreateCheckoutSession');
// const manageSubscription = httpsCallable(functions, 'stripeManageSubscription');

const MonetizationPage: React.FC = () => {
  const { t } = useTranslation();
  const { lang } = useLang(); // Pour les liens légaux spécifiques à la langue
  const theme = useContext(ThemeContext) as DefaultTheme;
  const navigate = useNavigate();

  // Simuler l'état de l'utilisateur et de son abonnement
  // const { currentUser } = useAuth(); // À utiliser quand AuthContext est prêt
  const [currentUser, setCurrentUser] = useState<CurrentUser | null>({ uid: "testUser123", isPremium: false }); // Placeholder
  const [subscription, setSubscription] = useState<SubscriptionDetails | null>(null);
  const [isLoadingSubscription, setIsLoadingSubscription] = useState(false); // Pour le statut de l'abonnement
  const [isProcessingPayment, setIsProcessingPayment] = useState(false);

  // Simuler le chargement des détails de l'abonnement de l'utilisateur
  useEffect(() => {
    if (currentUser) {
      setIsLoadingSubscription(true);
      // const subRef = doc(db, "users", currentUser.uid, "subscriptions", "primary"); // "primary" ou l'ID de l'abonnement
      // const unsubscribe = onSnapshot(subRef, (docSnap) => {
      //   if (docSnap.exists()) {
      //     const subData = docSnap.data() as SubscriptionDetails;
      //     setSubscription(subData);
      //     setCurrentUser(prev => ({...prev!, isPremium: subData.status === 'active' || subData.status === 'trialing' }));
      //   } else {
      //     setSubscription(null);
      //     setCurrentUser(prev => ({...prev!, isPremium: false }));
      //   }
      //   setIsLoadingSubscription(false);
      // }, (error) => {
      //   console.error("Error fetching subscription details:", error);
      //   setIsLoadingSubscription(false);
      // });
      // return () => unsubscribe();

      // Simulation
      setTimeout(() => {
        if (currentUser.uid === "testUserPremium") { // Simuler un utilisateur premium
            setSubscription({ status: 'active', current_period_end: (Date.now() / 1000) + (30 * 24 * 60 * 60), cancel_at_period_end: false });
            setCurrentUser(prev => ({...prev!, isPremium: true, subscriptionEnds: new Date(Date.now() + (30 * 24 * 60 * 60 * 1000)) }));
        } else {
            setSubscription(null);
            setCurrentUser(prev => ({...prev!, isPremium: false }));
        }
        setIsLoadingSubscription(false);
      }, 1000);
    } else {
        setIsLoadingSubscription(false);
    }
  }, [currentUser?.uid]); // Dépend de currentUser.uid

  const handleSubscribe = async () => {
    if (!currentUser) {
      // Rediriger vers la page de connexion/inscription
      navigate('/profile'); // Ou votre page de connexion
      return;
    }
    setIsProcessingPayment(true);
    try {
      // const { data } = await createCheckoutSession({ priceId: 'VOTRE_PRICE_ID_STRIPE_MENSUEL', returnUrl: window.location.href });
      // window.location.href = data.url; // Redirection vers la page de paiement Stripe
      console.log("Redirection vers la page de paiement (simulation)... pour user:", currentUser.uid);
      alert("Simulation : Redirection vers la page de paiement. Dans une vraie app, vous seriez redirigé vers Stripe/Paddle.");
      // Simuler un succès après un délai
      setTimeout(() => {
        setCurrentUser(prev => ({...prev!, isPremium: true, subscriptionEnds: new Date(Date.now() + (30 * 24 * 60 * 60 * 1000))}));
        setSubscription({ status: 'active', current_period_end: (Date.now() / 1000) + (30 * 24 * 60 * 60), cancel_at_period_end: false });
        setIsProcessingPayment(false);
      }, 2000);
    } catch (error) {
      console.error("Erreur lors de la création de la session de paiement:", error);
      alert(t('errors.paymentError', "Une erreur s'est produite lors du traitement de votre paiement."));
      setIsProcessingPayment(false);
    }
  };

  const handleManageSubscription = async () => {
    if (!currentUser) return;
    setIsProcessingPayment(true); // Utiliser le même loader
    try {
      // const { data } = await manageSubscription({ returnUrl: window.location.href });
      // window.location.href = data.url; // Redirection vers le portail client Stripe/Paddle
      console.log("Redirection vers le portail de gestion d'abonnement (simulation)...");
      alert(t('monetization.manageSubAlert', "Simulation : Redirection vers le portail de gestion d'abonnement. Vous pourriez annuler ou mettre à jour votre abonnement ici."));
      // Simuler une annulation pour la démo
      setTimeout(() => {
        setSubscription(prev => prev ? {...prev, status: 'canceled', cancel_at_period_end: true} : null );
        // Note: isPremium resterait true jusqu'à la fin de la période actuelle
        setIsProcessingPayment(false);
      }, 1500);
    } catch (error) {
      console.error("Erreur lors de l'accès au portail de gestion:", error);
      alert(t('errors.manageSubscriptionError', "Impossible d'accéder à la gestion de votre abonnement."));
      setIsProcessingPayment(false);
    }
  };

  const handleDonate = () => {
    // Rediriger vers PayPal ou une autre plateforme de don
    alert(t('monetization.donateAlert', "Merci pour votre intérêt ! La fonctionnalité de don sera bientôt disponible. En attendant, vous pouvez visiter notre site."));
    // window.open('https://votre_lien_paypal_ou_autre', '_blank');
  };

  const freeFeatures = [
    t('features.free.baseMeditations', "Accès aux méditations et histoires de base"),
    t('features.free.backgroundMusic', "Musique de fond et voix TTS basiques"),
    t('features.free.stats', "Statistiques de progression"),
    t('features.free.blog', "Accès au blog communautaire"),
    t('features.free.leaderboard', "Participation au leaderboard anonyme"),
  ];

  const premiumFeatures = [
    ...freeFeatures,
    t('features.premium.allSessions', "Accès illimité à TOUTES les séances (hypnose, PNL, etc.)"),
    t('features.premium.ambientSounds', "Sons d'ambiance et binauraux avancés"),
    t('features.premium.customSessions', "Création de séances personnalisées"),
    t('features.premium.games', "Accès aux mini-jeux de pleine conscience"),
    t('features.premium.journal', "Journal de suivi détaillé"),
    t('features.premium.motivationNotifs', "Notifications de motivation personnalisées"),
    t('features.premium.calendar', "Calendrier et programmes personnalisés (à venir)"),
    t('features.premium.customAudio', "Possibilité d'utiliser vos propres sons et musiques"),
    t('features.premium.noAds', "Expérience sans publicité"),
    t('features.premium.prioritySupport', "Support prioritaire"),
  ];

  const renderFeatureList = (features: string[], isPremiumPlan: boolean) => (
    <FeatureList>
      {premiumFeatures.map((feature, index) => {
        const isEnabled = features.includes(feature);
        return (
          <li key={index} className={!isEnabled && isPremiumPlan ? 'disabled-feature' : ''}>
            {isEnabled ? <FiCheckCircle style={{color: theme.primary}}/> : (isPremiumPlan ? <FiXCircle /> : <FiCheckCircle style={{color: theme.primary}}/>) }
            {feature}
          </li>
        );
      })}
    </FeatureList>
  );


  if (isLoadingSubscription && !currentUser) { // Attendre que currentUser soit chargé si on dépend de lui
      return <LoadingContainer><FiLoader /> {t('loading.user', 'Chargement des informations utilisateur...')}</LoadingContainer>;
  }

  return (
    <PageContainer>
      <PageHeader>
        <h1><FiDollarSign /> {t('monetization.title', 'Nos Plans & Soutien')}</h1>
        <p>{t('monetization.description', 'Choisissez le plan qui vous convient ou soutenez notre mission pour continuer à vous offrir des outils de bien-être accessibles.')}</p>
      </PageHeader>

      {currentUser && subscription && (subscription.status === 'active' || subscription.status === 'trialing') && (
        <CurrentSubscriptionInfo>
          <h4>{t('monetization.currentPlan', 'Votre Plan Actuel : Premium')} <FiStar style={{color: theme.primary}}/></h4>
          {subscription.cancel_at_period_end ? (
            <p>{t('monetization.subCancelsOn', 'Votre abonnement expirera le')} <span className="renewal-date">{new Date((subscription.current_period_end || 0) * 1000).toLocaleDateString(lang)}.</span></p>
          ) : (
            <p>{t('monetization.subRenewsOn', 'Prochain renouvellement le')} <span className="renewal-date">{new Date((subscription.current_period_end || 0) * 1000).toLocaleDateString(lang)}.</span></p>
          )}
          <Button onClick={handleManageSubscription} disabled={isProcessingPayment} style={{marginTop: '0.5rem', background: theme.secondary}}>
            {isProcessingPayment ? <FiLoader style={{animation: 'spin 1s linear infinite'}}/> : <FiSettings />}
            {t('monetization.manageSub', 'Gérer mon abonnement')}
          </Button>
        </CurrentSubscriptionInfo>
      )}

      <PlansContainer>
        <PlanCard $isCurrentPlan={currentUser != null && !currentUser.isPremium}>
          <PlanHeader>
            <div className="plan-icon"><FiZap /></div>
            <h3>{t('plans.free.title', 'Plan Gratuit')}</h3>
            <div className="price">{t('plans.free.price', '0$')} <span>/{t('plans.billing.month', 'mois')}</span></div>
          </PlanHeader>
          {renderFeatureList(freeFeatures, false)}
          {currentUser && currentUser.isPremium ? (
             <Button onClick={handleManageSubscription} disabled={isProcessingPayment}>
                {isProcessingPayment ? <FiLoader style={{animation: 'spin 1s linear infinite'}}/> : <FiRepeat />}
                {t('plans.free.switchToFree', 'Passer au plan Gratuit')}
            </Button>
          ) : (
            <Button disabled style={{opacity: 0.7, cursor: 'default'}}>
                <FiCheckCircle /> {t('plans.free.currentPlan', 'Votre Plan Actuel')}
            </Button>
          )}
        </PlanCard>

        <PlanCard $isPremium $isCurrentPlan={currentUser != null && currentUser.isPremium === true}>
          <PlanHeader>
            <div className="plan-icon"><FiStar /></div>
            <h3>{t('plans.premium.title', 'Piknowkyo Premium')}</h3>
            <div className="price">{PREMIUM_PRICE_USD_MONTHLY}{PREMIUM_CURRENCY} <span>/{t('plans.billing.month', 'mois')}</span></div>
            <div className="billing-cycle">{t('plans.premium.billedMonthly', 'Facturé mensuellement, annulez à tout moment.')}</div>
          </PlanHeader>
          {renderFeatureList(premiumFeatures, true)}
          {currentUser && currentUser.isPremium ? (
            <Button onClick={handleManageSubscription} disabled={isProcessingPayment}>
                <FiSettings /> {t('plans.premium.manageSub', 'Gérer l\'Abonnement')}
            </Button>
          ) : (
            <Button onClick={handleSubscribe} disabled={isProcessingPayment}>
              {isProcessingPayment ? <FiLoader style={{animation: 'spin 1s linear infinite'}}/> : <FiZap />}
              {t('plans.premium.subscribe', 'Passer à Premium')}
            </Button>
          )}
        </PlanCard>
      </PlansContainer>

      <DonationCard>
        <PlanHeader>
          <div className="plan-icon"><FiGift /></div>
          <h3>{t('monetization.donateTitle', 'Soutenir PiKnowKyo')}</h3>
          <p style={{fontSize: '1rem', color: theme.textSecondary, marginBottom: '1.5rem'}}>{t('monetization.donateDescription', 'Votre générosité nous aide à maintenir et améliorer l\'application pour tous. Chaque contribution compte !')}</p>
        </PlanHeader>
        <Button onClick={handleDonate} style={{background: theme.accent, width: '100%'}} >
          <FiHeart /> {t('monetization.donateButton', 'Faire un don')}
        </Button>
      </DonationCard>

      <LegalLinks>
        <Link to={`https://piknowkyo.com/privacy_${lang}.html`} target="_blank" rel="noopener noreferrer">{t('legal.privacy', 'Politique de confidentialité')}</Link>
        |
        <Link to={`https://piknowkyo.com/terms_${lang}.html`} target="_blank" rel="noopener noreferrer">{t('legal.terms', 'Termes et conditions')}</Link>
      </LegalLinks>
    </PageContainer>
  );
};

export default MonetizationPage;