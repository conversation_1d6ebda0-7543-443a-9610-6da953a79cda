// src/games/common/gameUtils.ts

import { SavedGameState, LeaderboardEntry } from './models';

const LOCAL_STORAGE_PREFIX = 'game_save_';
const LEADERBOARD_PREFIX = 'game_leaderboard_'; // Pour une simulation de leaderboard local

/**
 * Sauvegarde l'état actuel d'un jeu pour un utilisateur.
 * @param gameId ID unique du jeu (ex: "zen-tetris")
 * @param userId ID de l'utilisateur
 * @param gameState L'objet SavedGameState à sauvegarder
 */
export const saveGameState = (gameId: string, userId: string, gameState: SavedGameState): void => {
  try {
    const key = `${LOCAL_STORAGE_PREFIX}${userId}_${gameId}`;
    localStorage.setItem(key, JSON.stringify(gameState));
    console.log(`Progression du jeu ${gameId} sauvegardée pour ${userId}.`);
  } catch (error) {
    console.error(`Erreur lors de la sauvegarde de la progression du jeu ${gameId}:`, error);
  }
};

/**
 * Charge l'état sauvegardé d'un jeu pour un utilisateur.
 * @param gameId ID unique du jeu
 * @param userId ID de l'utilisateur
 * @returns L'objet SavedGameState ou null si aucune sauvegarde n'existe.
 */
export const loadGameState = (gameId: string, userId: string): SavedGameState | null => {
  try {
    const key = `${LOCAL_STORAGE_PREFIX}${userId}_${gameId}`;
    const saved = localStorage.getItem(key);
    if (saved) {
      console.log(`Progression du jeu ${gameId} chargée pour ${userId}.`);
      const parsed = JSON.parse(saved);
      // Assurez-vous que lastPlayed est bien un objet Date
      if (parsed.lastPlayed) parsed.lastPlayed = new Date(parsed.lastPlayed);
      return parsed as SavedGameState;
    }
  } catch (error) {
    console.error(`Erreur lors du chargement de la progression du jeu ${gameId}:`, error);
  }
  return null;
};

/**
 * Efface l'état sauvegardé d'un jeu pour un utilisateur.
 * @param gameId ID unique du jeu
 * @param userId ID de l'utilisateur
 */
export const clearGameState = (gameId: string, userId: string): void => {
  try {
    const key = `${LOCAL_STORAGE_PREFIX}${userId}_${gameId}`;
    localStorage.removeItem(key);
    console.log(`Progression du jeu ${gameId} effacée pour ${userId}.`);
  } catch (error) {
    console.error(`Erreur lors de l'effacement de la progression du jeu ${gameId}:`, error);
  }
};

/**
 * Soumet le score d'une partie terminée au leaderboard (simulation locale).
 * Dans une application réelle, cela ferait un appel API à votre backend.
 * @param entry L'entrée LeaderboardEntry à soumettre.
 */
export const submitScoreToLeaderboard = (entry: Omit<LeaderboardEntry, 'timestamp'>): void => { // Omit timestamp car généré ici
    try {
        const fullEntry: LeaderboardEntry = { ...entry, timestamp: new Date() };
        const key = `${LEADERBOARD_PREFIX}${fullEntry.gameId}`;
        const existingEntries: LeaderboardEntry[] = JSON.parse(localStorage.getItem(key) || '[]');
        
        // Ajouter la nouvelle entrée
        existingEntries.push(fullEntry);
        
        // Pour une simulation simple, trier et ne garder que les 10 meilleurs scores par jeu
        existingEntries.sort((a, b) => b.score - a.score || b.levelReached - a.levelReached || b.timestamp.getTime() - a.timestamp.getTime());
        const topScores = existingEntries.slice(0, 10); // Garder les 10 meilleurs
        
        localStorage.setItem(key, JSON.stringify(topScores));
        console.log(`Score soumis au leaderboard pour ${entry.gameId}:`, fullEntry);
    } catch (error) {
        console.error("Erreur lors de la soumission du score au leaderboard:", error);
    }
};

/**
 * Récupère le leaderboard pour un jeu donné (simulation locale).
 * @param gameId ID unique du jeu
 * @returns Tableau des entrées du leaderboard.
 */
export const getLeaderboard = (gameId: string): LeaderboardEntry[] => {
    try {
        const key = `${LEADERBOARD_PREFIX}${gameId}`;
        const entries = JSON.parse(localStorage.getItem(key) || '[]');
        // Convertir les timestamps en Date
        return entries.map((entry: any) => ({ ...entry, timestamp: new Date(entry.timestamp) }));
    } catch (error) {
        console.error("Erreur lors de la récupération du leaderboard:", error);
        return [];
    }
};

/**
 * Récupère le meilleur score personnel pour un jeu donné et un utilisateur.
 * @param gameId ID unique du jeu
 * @param userId ID de l'utilisateur
 * @returns Le meilleur score ou 0 si aucun.
 */
export const getPersonalBestScore = (gameId: string, userId: string): number => {
    const leaderboard = getLeaderboard(gameId);
    const personalEntries = leaderboard.filter(entry => entry.userId === userId);
    if (personalEntries.length === 0) return 0;
    return Math.max(...personalEntries.map(entry => entry.score));
};