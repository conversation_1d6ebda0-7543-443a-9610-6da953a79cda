import { useDispatch, useSelector, TypedUseSelectorHook } from 'react-redux';
import type { RootState, AppDispatch } from './index';

// Hooks typés pour Redux
export const useAppDispatch = () => useDispatch<AppDispatch>();
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;

// Sélecteurs personnalisés
export const useAuth = () => {
  return useAppSelector((state) => ({
    user: state.auth.user,
    isAuthenticated: state.auth.isAuthenticated,
    isLoading: state.auth.isLoading,
    error: state.auth.error,
    lastSyncTimestamp: state.auth.lastSyncTimestamp,
  }));
};

export const useSessions = () => {
  return useAppSelector((state) => ({
    sessions: state.sessions.sessions,
    currentSession: state.sessions.currentSession,
    isLoading: state.sessions.isLoading,
    error: state.sessions.error,
    lastSyncTimestamp: state.sessions.lastSyncTimestamp,
    pendingChanges: state.sessions.pendingChanges,
  }));
};

export const useJournal = () => {
  return useAppSelector((state) => ({
    entries: state.journal.entries,
    isLoading: state.journal.isLoading,
    error: state.journal.error,
    lastSyncTimestamp: state.journal.lastSyncTimestamp,
    pendingChanges: state.journal.pendingChanges,
  }));
};

export const useAudioAssets = () => {
  return useAppSelector((state) => ({
    assets: state.audioAssets.assets,
    manifest: state.audioAssets.manifest,
    isLoading: state.audioAssets.isLoading,
    error: state.audioAssets.error,
    lastSyncTimestamp: state.audioAssets.lastSyncTimestamp,
    pendingChanges: state.audioAssets.pendingChanges,
  }));
};

export const useSync = () => {
  return useAppSelector((state) => ({
    isOnline: state.sync.isOnline,
    isSyncing: state.sync.isSyncing,
    lastSyncTimestamp: state.sync.lastSyncTimestamp,
    syncErrors: state.sync.syncErrors,
    pendingSyncCount: state.sync.pendingSyncCount,
    syncStatus: state.sync.syncStatus,
    conflicts: state.sync.conflictResolution.conflicts,
    conflictStrategy: state.sync.conflictResolution.strategy,
  }));
};

export const useNetwork = () => {
  return useAppSelector((state) => ({
    isOnline: state.network.isOnline,
    connectionType: state.network.connectionType,
    isSlowConnection: state.network.isSlowConnection,
    lastOnlineTimestamp: state.network.lastOnlineTimestamp,
    offlineDuration: state.network.offlineDuration,
  }));
};

// Hooks composés pour des cas d'usage spécifiques
export const useOfflineStatus = () => {
  const network = useNetwork();
  const sync = useSync();
  
  return {
    isOffline: !network.isOnline,
    hasPendingChanges: sync.pendingSyncCount > 0,
    canSync: network.isOnline && !sync.isSyncing,
    syncStatus: sync.syncStatus,
    offlineDuration: network.offlineDuration,
  };
};
