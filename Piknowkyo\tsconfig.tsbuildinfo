{"root": ["./src/app.test.tsx", "./src/app.tsx", "./src/globalstyles.tsx", "./src/langprovider.tsx", "./src/themeprovider.tsx", "./src/firebase.ts", "./src/i18n.ts", "./src/index.tsx", "./src/models.ts", "./src/react-app-env.d.ts", "./src/reportwebvitals.ts", "./src/setuptests.ts", "./src/styled.d.ts", "./src/themes.ts", "./src/vite-env.d.ts", "./src/components/adrewardmodal.tsx", "./src/components/audioconfigpanel.tsx", "./src/components/authpage.tsx", "./src/components/bottombar.tsx", "./src/components/journalentryform.tsx", "./src/components/languageswitcher.tsx", "./src/components/login.tsx", "./src/components/logout.tsx", "./src/components/mainmenu.tsx", "./src/components/networkstatusnotifier.tsx", "./src/components/notificationtest.tsx", "./src/components/preferences.tsx", "./src/components/premiumgate.tsx", "./src/components/questionnaire.tsx", "./src/components/reduxexample.tsx", "./src/components/reusablemodal.tsx", "./src/components/sessionslist.tsx", "./src/components/signupform.tsx", "./src/components/splashscreen.tsx", "./src/components/syncstatusindicator.tsx", "./src/components/toast.tsx", "./src/components/updatenotification.tsx", "./src/components/authstatus.tsx", "./src/components/common/authstyles.ts", "./src/components/common/button.tsx", "./src/components/common/card.tsx", "./src/components/common/index.ts", "./src/components/ui/button.tsx", "./src/components/ui/card.tsx", "./src/components/ui/index.ts", "./src/data/audioassets.ts", "./src/data/sessions.ts", "./src/games/common/gameutils.ts", "./src/games/common/models.tsx", "./src/games/common/components/gamemodal.tsx", "./src/games/common/components/gametimer.tsx", "./src/games/common/components/scoredisplay.tsx", "./src/games/zen-tetris/gamecomponent.tsx", "./src/games/zen-tetris/logic.ts", "./src/games/zen-tetris/styles.ts", "./src/games/zen-tetris/components/orientationhint.tsx", "./src/games/zen-tetris/hooks/useorientation.ts", "./src/games/zen-tetris/utils/haptics.ts", "./src/hooks/useads.ts", "./src/hooks/useauth.ts", "./src/hooks/usesubscription.ts", "./src/models/script.d.ts", "./src/models/script.model.ts", "./src/pages/aboutpage.tsx", "./src/pages/audioassetsconfigpage.tsx", "./src/pages/blogpage.tsx", "./src/pages/blogpostcommentspage.tsx", "./src/pages/categoriespage.tsx", "./src/pages/gamespage.tsx", "./src/pages/historypage.tsx", "./src/pages/homepage.tsx", "./src/pages/journalpage.tsx", "./src/pages/leaderboardpage.tsx", "./src/pages/monetizationpage.tsx", "./src/pages/notfoundpage.tsx", "./src/pages/playerpage.tsx", "./src/pages/profilepage.tsx", "./src/pages/quizpage.tsx", "./src/pages/sessiondetailpage.tsx", "./src/pages/sessionspage.tsx", "./src/pages/settingspage.tsx", "./src/pages/statspage.tsx", "./src/services/adservice.ts", "./src/services/firebase.ts", "./src/services/pipervoices.ts", "./src/services/scriptsservice.ts", "./src/services/serviceworkermanager.ts", "./src/services/syncservice.ts", "./src/services/tts.ts", "./src/services/ttsvoices.ts", "./src/services/updateservice.ts", "./src/services/usenetworkstatus.ts", "./src/services/usepushnotifications.ts", "./src/store/hooks.ts", "./src/store/index.ts", "./src/store/slices/audioassetsslice.ts", "./src/store/slices/authslice.ts", "./src/store/slices/journalslice.ts", "./src/store/slices/networkslice.ts", "./src/store/slices/sessionsslice.ts", "./src/store/slices/subscriptionslice.ts", "./src/store/slices/syncslice.ts"], "errors": true, "version": "5.8.3"}