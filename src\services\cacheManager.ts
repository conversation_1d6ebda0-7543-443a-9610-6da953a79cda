// Cache Manager for Piknowkyo
// Handles IndexedDB storage and offline data management

import localforage from 'localforage';

export interface CacheConfig {
  name: string;
  version: number;
  description: string;
}

export interface CachedItem<T = any> {
  data: T;
  timestamp: number;
  version: string;
  expiresAt?: number;
}

class CacheManager {
  private stores: Map<string, LocalForage> = new Map();
  private defaultConfig: CacheConfig = {
    name: 'piknowkyo',
    version: 1,
    description: 'Piknowkyo offline storage'
  };

  constructor() {
    this.initializeStores();
  }

  /**
   * Initialize storage instances
   */
  private initializeStores(): void {
    // Sessions cache
    this.stores.set('sessions', localforage.createInstance({
      name: 'piknowkyo-sessions',
      storeName: 'sessions',
      description: 'Meditation sessions and scripts'
    }));

    // Journal cache
    this.stores.set('journal', localforage.createInstance({
      name: 'piknowkyo-journal',
      storeName: 'journal',
      description: 'User journal entries'
    }));

    // Audio assets cache
    this.stores.set('audio', localforage.createInstance({
      name: 'piknowkyo-audio',
      storeName: 'audio',
      description: 'Audio files and metadata'
    }));

    // User data cache
    this.stores.set('user', localforage.createInstance({
      name: 'piknowkyo-user',
      storeName: 'user',
      description: 'User profile and preferences'
    }));

    // App metadata cache
    this.stores.set('metadata', localforage.createInstance({
      name: 'piknowkyo-metadata',
      storeName: 'metadata',
      description: 'App metadata and sync info'
    }));
  }

  /**
   * Get storage instance
   */
  private getStore(storeName: string): LocalForage {
    const store = this.stores.get(storeName);
    if (!store) {
      throw new Error(`Store '${storeName}' not found`);
    }
    return store;
  }

  /**
   * Set item in cache with expiration
   */
  async setItem<T>(
    storeName: string, 
    key: string, 
    data: T, 
    expirationHours?: number
  ): Promise<void> {
    const store = this.getStore(storeName);
    
    const cachedItem: CachedItem<T> = {
      data,
      timestamp: Date.now(),
      version: '1.0.0',
      expiresAt: expirationHours ? Date.now() + (expirationHours * 60 * 60 * 1000) : undefined
    };

    await store.setItem(key, cachedItem);
  }

  /**
   * Get item from cache
   */
  async getItem<T>(storeName: string, key: string): Promise<T | null> {
    const store = this.getStore(storeName);
    
    try {
      const cachedItem: CachedItem<T> | null = await store.getItem(key);
      
      if (!cachedItem) {
        return null;
      }

      // Check expiration
      if (cachedItem.expiresAt && Date.now() > cachedItem.expiresAt) {
        await this.removeItem(storeName, key);
        return null;
      }

      return cachedItem.data;
    } catch (error) {
      console.error(`[Cache] Failed to get item ${key} from ${storeName}:`, error);
      return null;
    }
  }

  /**
   * Remove item from cache
   */
  async removeItem(storeName: string, key: string): Promise<void> {
    const store = this.getStore(storeName);
    await store.removeItem(key);
  }

  /**
   * Clear entire store
   */
  async clearStore(storeName: string): Promise<void> {
    const store = this.getStore(storeName);
    await store.clear();
  }

  /**
   * Get all keys in store
   */
  async getKeys(storeName: string): Promise<string[]> {
    const store = this.getStore(storeName);
    return await store.keys();
  }

  /**
   * Get cache size information
   */
  async getCacheInfo(storeName: string): Promise<{
    itemCount: number;
    keys: string[];
    estimatedSize: number;
  }> {
    const store = this.getStore(storeName);
    const keys = await store.keys();
    
    let estimatedSize = 0;
    for (const key of keys) {
      try {
        const item = await store.getItem(key);
        if (item) {
          estimatedSize += JSON.stringify(item).length;
        }
      } catch (error) {
        console.warn(`[Cache] Failed to calculate size for ${key}:`, error);
      }
    }

    return {
      itemCount: keys.length,
      keys,
      estimatedSize
    };
  }

  /**
   * Clean expired items
   */
  async cleanExpiredItems(storeName: string): Promise<number> {
    const store = this.getStore(storeName);
    const keys = await store.keys();
    let cleanedCount = 0;

    for (const key of keys) {
      try {
        const cachedItem: CachedItem | null = await store.getItem(key);
        
        if (cachedItem?.expiresAt && Date.now() > cachedItem.expiresAt) {
          await store.removeItem(key);
          cleanedCount++;
        }
      } catch (error) {
        console.warn(`[Cache] Failed to check expiration for ${key}:`, error);
      }
    }

    return cleanedCount;
  }

  /**
   * Batch set items
   */
  async setItems<T>(
    storeName: string, 
    items: Array<{ key: string; data: T; expirationHours?: number }>
  ): Promise<void> {
    const promises = items.map(item => 
      this.setItem(storeName, item.key, item.data, item.expirationHours)
    );
    
    await Promise.all(promises);
  }

  /**
   * Batch get items
   */
  async getItems<T>(storeName: string, keys: string[]): Promise<Array<{ key: string; data: T | null }>> {
    const promises = keys.map(async key => ({
      key,
      data: await this.getItem<T>(storeName, key)
    }));
    
    return await Promise.all(promises);
  }

  /**
   * Check if item exists and is not expired
   */
  async hasValidItem(storeName: string, key: string): Promise<boolean> {
    const item = await this.getItem(storeName, key);
    return item !== null;
  }

  /**
   * Get cache statistics for all stores
   */
  async getAllCacheStats(): Promise<Record<string, any>> {
    const stats: Record<string, any> = {};
    
    for (const [storeName] of this.stores) {
      try {
        stats[storeName] = await this.getCacheInfo(storeName);
      } catch (error) {
        stats[storeName] = { error: error instanceof Error ? error.message : 'Unknown error' };
      }
    }

    return stats;
  }

  /**
   * Clean all expired items across all stores
   */
  async cleanAllExpiredItems(): Promise<Record<string, number>> {
    const results: Record<string, number> = {};
    
    for (const [storeName] of this.stores) {
      try {
        results[storeName] = await this.cleanExpiredItems(storeName);
      } catch (error) {
        console.error(`[Cache] Failed to clean expired items in ${storeName}:`, error);
        results[storeName] = 0;
      }
    }

    return results;
  }

  /**
   * Clear all caches (for reset/logout)
   */
  async clearAllCaches(): Promise<void> {
    const promises = Array.from(this.stores.keys()).map(storeName => 
      this.clearStore(storeName)
    );
    
    await Promise.all(promises);
  }

  /**
   * Export cache data for backup
   */
  async exportCacheData(storeName: string): Promise<Record<string, any>> {
    const store = this.getStore(storeName);
    const keys = await store.keys();
    const data: Record<string, any> = {};

    for (const key of keys) {
      try {
        data[key] = await store.getItem(key);
      } catch (error) {
        console.warn(`[Cache] Failed to export ${key}:`, error);
      }
    }

    return data;
  }

  /**
   * Import cache data from backup
   */
  async importCacheData(storeName: string, data: Record<string, any>): Promise<void> {
    const store = this.getStore(storeName);
    
    for (const [key, value] of Object.entries(data)) {
      try {
        await store.setItem(key, value);
      } catch (error) {
        console.warn(`[Cache] Failed to import ${key}:`, error);
      }
    }
  }
}

// Export singleton instance
export const cacheManager = new CacheManager();
export default cacheManager;
