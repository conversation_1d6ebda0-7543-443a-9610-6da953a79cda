#!/usr/bin/env node

// Custom Production Readiness Test for Piknowkyo
const fs = require('fs');
const path = require('path');

console.log('🚀 Piknowkyo Custom Production Test\n');

// Test results
const results = { passed: 0, failed: 0, tests: [] };

function test(name, condition) {
  const status = condition ? '✅' : '❌';
  const result = condition ? 'PASS' : 'FAIL';
  
  console.log(`${status} ${name}: ${result}`);
  
  results.tests.push({ name, status: result });
  
  if (condition) {
    results.passed++;
  } else {
    results.failed++;
  }
}

function fileExists(filePath) {
  return fs.existsSync(path.join(__dirname, filePath));
}

function fileContains(filePath, content) {
  if (!fileExists(filePath)) return false;
  const fileContent = fs.readFileSync(path.join(__dirname, filePath), 'utf8');
  return fileContent.includes(content);
}

console.log('📋 Testing Core Implementation...\n');

// Test core files
test('Service Worker exists', fileExists('public/sw.js'));
test('Service Worker Manager exists', fileExists('src/services/serviceWorkerManager.ts'));
test('Update Service exists', fileExists('src/services/updateService.ts'));
test('Update Notification exists', fileExists('src/components/UpdateNotification.tsx'));
test('Subscription Slice exists', fileExists('src/store/slices/subscriptionSlice.ts'));
test('Ad Service exists', fileExists('src/services/adService.ts'));
test('Stripe Service exists', fileExists('src/services/stripeService.ts'));
test('Premium Gate exists', fileExists('src/components/PremiumGate.tsx'));
test('Ad Reward Modal exists', fileExists('src/components/AdRewardModal.tsx'));
test('Subscription Hook exists', fileExists('src/hooks/useSubscription.ts'));
test('Ad Hook exists', fileExists('src/hooks/useAds.ts'));
test('Cache Manager exists', fileExists('src/services/cacheManager.ts'));

console.log('\n🔧 Testing Integration...\n');

// Test integration
test('Service Worker registered in index', fileContains('src/index.tsx', 'serviceWorkerManager'));
test('Update Notification in App', fileContains('src/App.tsx', 'UpdateNotification'));
test('Subscription slice in store', fileContains('src/store/index.ts', 'subscriptionSlice'));
test('ProfilePage uses subscription', fileContains('src/pages/ProfilePage.tsx', 'useSubscription'));

console.log('\n📱 Testing PWA...\n');

// Test PWA
const manifest = JSON.parse(fs.readFileSync('public/manifest.json', 'utf8'));
test('PWA Manifest has Piknowkyo name', manifest.name.includes('PiKnowKyo'));
test('PWA Manifest has shortcuts', manifest.shortcuts && manifest.shortcuts.length > 0);
test('Version file exists', fileExists('public/version.json'));

console.log('\n📦 Testing Dependencies...\n');

// Test dependencies
const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
const deps = { ...packageJson.dependencies, ...packageJson.devDependencies };

test('Stripe JS installed', deps['@stripe/stripe-js'] !== undefined);
test('LocalForage installed', deps.localforage !== undefined);
test('Redux Toolkit installed', deps['@reduxjs/toolkit'] !== undefined);
test('Firebase installed', deps.firebase !== undefined);

console.log('\n📊 Summary...\n');

console.log(`✅ Passed: ${results.passed}`);
console.log(`❌ Failed: ${results.failed}`);
console.log(`📋 Total: ${results.tests.length}`);

const passRate = ((results.passed / results.tests.length) * 100).toFixed(1);
console.log(`📈 Pass Rate: ${passRate}%`);

if (results.failed === 0) {
  console.log('\n🟢 EXCELLENT - All tests passed!');
} else if (results.failed <= 2) {
  console.log('\n🟡 GOOD - Minor issues to fix');
} else {
  console.log('\n🔴 NEEDS WORK - Several issues to address');
}

if (results.failed > 0) {
  console.log('\n❌ Failed Tests:');
  results.tests.filter(t => t.status === 'FAIL').forEach(test => {
    console.log(`   • ${test.name}`);
  });
}

console.log('\n🚀 Next Steps:');
console.log('   1. Fix any failed tests');
console.log('   2. Run: npm run build');
console.log('   3. Test manually in browser');
console.log('   4. Deploy: npm run deploy');

process.exit(results.failed > 0 ? 1 : 0);
