// src/store/slices/historySlice.ts
import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { collection, getDocs, addDoc, query, orderBy, Timestamp } from 'firebase/firestore';
import { db } from '../../firebase'; // Assurez-vous que 'db' (Firestore) est bien exporté de firebase.ts

export interface HistoryEntry {
  id: string;
  userId: string; // Lien avec l'utilisateur
  type: 'session' | 'game' | 'meditation' | 'exercise' | 'custom'; // Type d'activité (ajouté 'custom')
  activityId?: string; // ID de la session/jeu/méditation si applicable
  name: string; // Nom de l'activité (ex: "Méditation du matin", "Tetris")
  duration?: number; // Durée en secondes
  timestamp: number; // Quand l'activité a eu lieu (en millisecondes Unix)
  details?: any; // Détails supplémentaires spécifiques à l'activité (peut être un objet)
}

interface HistoryState {
  entries: HistoryEntry[];
  loading: boolean;
  error: string | null;
}

const initialState: HistoryState = {
  entries: [],
  loading: false,
  error: null,
};

// Thunk pour récupérer l'historique d'un utilisateur spécifique
export const fetchHistory = createAsyncThunk(
  'history/fetchHistory',
  async (userId: string, { rejectWithValue }) => {
    try {
      if (!userId) {
        throw new Error("User ID is required to fetch history.");
      }
      const historyCollectionRef = collection(db, 'users', userId, 'history');
      const q = query(historyCollectionRef, orderBy('timestamp', 'desc'));
      const querySnapshot = await getDocs(q);

      const entries = querySnapshot.docs.map(doc => {
        const data = doc.data();
        return {
          id: doc.id,
          ...data,
          // Convertir Timestamp en number (millisecondes Unix)
          timestamp: data.timestamp instanceof Timestamp ? data.timestamp.toDate().getTime() : data.timestamp,
        } as HistoryEntry;
      });
      return entries;
    } catch (error: any) {
      console.error("Error fetching user history:", error);
      return rejectWithValue(error.message || "Failed to fetch history.");
    }
  }
);

// Thunk pour ajouter une nouvelle entrée à l'historique d'un utilisateur
export const addHistoryEntry = createAsyncThunk(
  'history/addHistoryEntry',
  async (
    { userId, entry }: { userId: string, entry: Omit<HistoryEntry, 'id' | 'timestamp' | 'userId'> },
    { rejectWithValue }
  ) => {
    try {
      if (!userId) {
        throw new Error("User ID is required to add history entry.");
      }
      const historyCollectionRef = collection(db, 'users', userId, 'history');
      // Stocker le timestamp comme un nombre (Unix millisecondes)
      const newEntryWithTimestamp = { ...entry, userId, timestamp: Date.now() };
      const docRef = await addDoc(historyCollectionRef, newEntryWithTimestamp);

      // Retourne l'entrée complète avec l'ID généré et le timestamp
      return { id: docRef.id, ...newEntryWithTimestamp } as HistoryEntry;
    } catch (error: any) {
      console.error("Error adding history entry:", error);
      return rejectWithValue(error.message || "Failed to add history entry.");
    }
  }
);

const historySlice = createSlice({
  name: 'history',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      // Cas pour fetchHistory
      .addCase(fetchHistory.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchHistory.fulfilled, (state, action: PayloadAction<HistoryEntry[]>) => {
        state.loading = false;
        state.entries = action.payload;
      })
      .addCase(fetchHistory.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // Cas pour addHistoryEntry
      .addCase(addHistoryEntry.pending, (state) => {
        state.error = null;
      })
      .addCase(addHistoryEntry.fulfilled, (state, action: PayloadAction<HistoryEntry>) => {
        state.entries.unshift(action.payload);
      })
      .addCase(addHistoryEntry.rejected, (state, action) => {
        state.error = action.payload as string;
      });
  },
});

export default historySlice.reducer;