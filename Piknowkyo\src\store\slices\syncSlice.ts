import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';

export interface SyncState {
  isOnline: boolean;
  isSyncing: boolean;
  lastSyncTimestamp: number | null;
  syncErrors: string[];
  pendingSyncCount: number;
  syncStatus: 'idle' | 'syncing' | 'success' | 'error';
  conflictResolution: {
    conflicts: Array<{
      id: string;
      type: 'session' | 'journal' | 'audioAsset';
      localData: any;
      remoteData: any;
      timestamp: number;
    }>;
    strategy: 'local' | 'remote' | 'manual';
  };
}

const initialState: SyncState = {
  isOnline: navigator.onLine,
  isSyncing: false,
  lastSyncTimestamp: null,
  syncErrors: [],
  pendingSyncCount: 0,
  syncStatus: 'idle',
  conflictResolution: {
    conflicts: [],
    strategy: 'manual',
  },
};

export const performFullSync = createAsyncThunk(
  'sync/performFullSync',
  async (_, { rejectWithValue }) => {
    try {
      console.log('Performing full sync...');
      
      // Simuler une synchronisation
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      return {
        syncedItems: 0,
        conflicts: 0,
        timestamp: Date.now(),
      };
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

export const syncPendingChanges = createAsyncThunk(
  'sync/syncPendingChanges',
  async (_, { rejectWithValue }) => {
    try {
      console.log('Syncing pending changes...');
      
      return {
        syncedCount: 0,
        timestamp: Date.now(),
      };
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

const syncSlice = createSlice({
  name: 'sync',
  initialState,
  reducers: {
    setOnlineStatus: (state, action: PayloadAction<boolean>) => {
      const wasOffline = !state.isOnline;
      state.isOnline = action.payload;
      
      if (wasOffline && action.payload) {
        state.syncStatus = 'idle';
      }
    },
    addSyncError: (state, action: PayloadAction<string>) => {
      state.syncErrors.push(action.payload);
    },
    clearSyncErrors: (state) => {
      state.syncErrors = [];
    },
    updatePendingSyncCount: (state, action: PayloadAction<number>) => {
      state.pendingSyncCount = action.payload;
    },
    resetSyncState: (state) => {
      state.isSyncing = false;
      state.syncStatus = 'idle';
      state.syncErrors = [];
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(performFullSync.pending, (state) => {
        state.isSyncing = true;
        state.syncStatus = 'syncing';
        state.syncErrors = [];
      })
      .addCase(performFullSync.fulfilled, (state, action) => {
        state.isSyncing = false;
        state.syncStatus = 'success';
        state.lastSyncTimestamp = action.payload.timestamp;
        state.pendingSyncCount = 0;
      })
      .addCase(performFullSync.rejected, (state, action) => {
        state.isSyncing = false;
        state.syncStatus = 'error';
        state.syncErrors.push(action.payload as string);
      })
      .addCase(syncPendingChanges.pending, (state) => {
        state.isSyncing = true;
        state.syncStatus = 'syncing';
      })
      .addCase(syncPendingChanges.fulfilled, (state, action) => {
        state.isSyncing = false;
        state.syncStatus = 'success';
        state.lastSyncTimestamp = action.payload.timestamp;
        state.pendingSyncCount = Math.max(0, state.pendingSyncCount - action.payload.syncedCount);
      })
      .addCase(syncPendingChanges.rejected, (state, action) => {
        state.isSyncing = false;
        state.syncStatus = 'error';
        state.syncErrors.push(action.payload as string);
      });
  },
});

export const {
  setOnlineStatus,
  addSyncError,
  clearSyncErrors,
  updatePendingSyncCount,
  resetSyncState,
} = syncSlice.actions;

export default syncSlice.reducer;
