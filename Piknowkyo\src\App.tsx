// src/App.tsx (CORRIGÉ POUR LA LOGIQUE DE PROFIL UTILISATEUR)
import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useLang, Language } from './LangProvider';
import { usePushNotifications } from './services/usePushNotifications';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { FiMenu } from 'react-icons/fi';
import { useAppDispatch, useAppSelector } from './store/hooks';

// Importez vos thunks
import { fetchSessions } from './store/slices/sessionsSlice';
import { fetchJournalEntries } from './store/slices/journalSlice';
import { fetchAudioAssets } from './store/slices/audioAssetsSlice';
// import { fetchHistory } from './store/slices/historySlice';
// Importez le nouveau thunk createUserProfile
// import { fetchUserProfile, createUserProfile, clearUserProfile } from './store/slices/userProfileSlice';
// import { fetchPricingConfig } from './store/slices/pricingSlice';

import HomePage from './pages/HomePage';
import AboutPage from './pages/AboutPage';
import SessionsPage from './pages/SessionsPage';
import SessionDetailPage from './pages/SessionDetailPage';
import PlayerPage from './pages/PlayerPage';
import JournalPage from './pages/JournalPage';
import StatsPage from './pages/StatsPage';
import LeaderboardPage from './pages/LeaderboardPage';
import BlogPage from './pages/BlogPage';
import BlogPostCommentsPage from './pages/BlogPostCommentsPage';
import GamesPage from './pages/GamesPage';
import MonetizationPage from './pages/MonetizationPage';
import CategoriesPage from './pages/CategoriesPage';
import HistoryPage from './pages/HistoryPage';
import SettingsPage from './pages/SettingsPage';
import AudioAssetsConfigPage from './pages/AudioAssetsConfigPage';
import NotFoundPage from './pages/NotFoundPage';
import ProfilePage from './pages/ProfilePage';
import ReduxExample from './components/ReduxExample';
import BottomBar from './components/BottomBar';
import NetworkStatusNotifier from './components/NetworkStatusNotifier';
import SplashScreen from './components/SplashScreen';
import MainMenu from './components/MainMenu';
import SyncStatusIndicator from './components/SyncStatusIndicator';
import UpdateNotification from './components/UpdateNotification';

import { useTheme } from './ThemeProvider';
import { useAuth } from './hooks/useAuth';
import AuthPage from './components/AuthPage';
import Logout from './components/Logout';
// import { AuthService } from './services/authService';

const App: React.FC = () => {
  const [showSplash, setShowSplash] = useState(true);
  const [menuOpen, setMenuOpen] = useState(false);
  const { lang, setLang } = useLang();
  const { i18n, t } = useTranslation();
  const { darkMode, toggleTheme } = useTheme();
  const dispatch = useAppDispatch();

  const { user, loading: authLoading } = useAuth(); // 'loading' est maintenant utilisé correctement
  // const { profile: userProfile, loading: profileLoading } = useAppSelector(state => state.userProfile);

  usePushNotifications();

  // useEffect(() => {
  //   AuthService.initialize();
  //   return () => AuthService.cleanup();
  // }, []);

  useEffect(() => {
    if (i18n.language !== lang) {
      i18n.changeLanguage(lang);
    }
  }, [lang, i18n]);

  useEffect(() => {
    const timer = setTimeout(() => setShowSplash(false), 1200);
    return () => clearTimeout(timer);
  }, []);

  // --- NOUVEAU: Chargement de TOUTES les données (globales et spécifiques à l'utilisateur) LORS DE LA CONNEXION ---
  useEffect(() => {
    if (!authLoading && user) { // L'utilisateur est connecté et l'état d'authentification est stable
      // Load basic data when user is authenticated
      dispatch(fetchSessions());
      dispatch(fetchAudioAssets());
      // 1. Charger le profil utilisateur (ou le créer s'il n'existe pas)
      // if (!userProfile && !profileLoading) {
      // Load journal entries for authenticated user
      if (user.uid) {
        dispatch(fetchJournalEntries(user.uid));
      }
    }
    // } else if (!authLoading && !user) {
    //     // Si l'utilisateur se déconnecte après le chargement initial de l'authentification
    //     // Nettoyer le state Redux des données utilisateurs si elles étaient présentes.
    //     dispatch(clearUserProfile()); // Nettoie le profil
    //     // Vous pouvez ajouter d'autres actions de nettoyage pour sessions, journal, etc.
    //     // dispatch(clearSessions());
    //     // dispatch(clearJournal());
    //     // ...
    // }
  }, [dispatch, user, authLoading]);

  const toggleMenu = () => {
    setMenuOpen(!menuOpen);
  };

  const closeMenu = () => {
    setMenuOpen(false);
  };

  // Condition de rendu :
  // On affiche le SplashScreen si:
  // 1. showSplash est vrai (au démarrage de l'app)
  // 2. authLoading est vrai (Firebase vérifie l'état d'authentification)
  if (showSplash || authLoading) {
    return <SplashScreen />;
  }

  // Si l'utilisateur n'est PAS connecté après le chargement initial de l'authentification
  if (!user) {
    return <AuthPage />;
  }
  // Si l'utilisateur est connecté et le profil a été chargé (ou créé après un échec initial)
  else {
    return (
      <Router>
        <div className={`app-container ${darkMode ? 'dark-theme' : 'light-theme'}`}>
          <header className="app-header">
            <div className="logo">
              <img src="/logo192.png" alt={t('app.logo_alt')} style={{ height: 40, marginRight: 12 }} />
              <span>{t('app.name')}</span>
            </div>
            <div className="header-actions">
              <SyncStatusIndicator />
              <Logout />
              <button
                className="theme-toggle"
                onClick={toggleTheme}
                aria-label={darkMode ? t('app.theme_light') : t('app.theme_dark')}
              >
                {darkMode ? '☀️' : '🌙'}
              </button>
              <button
                className="menu-toggle"
                onClick={toggleMenu}
                aria-label={t('main_menu.open_menu_aria_label')}
              >
                <FiMenu size={24} />
              </button>
            </div>
          </header>

          <MainMenu isOpen={menuOpen} onClose={closeMenu} />

          <main className="app-main">
            <Routes>
              <Route path="/" element={<HomePage />} />
              <Route path="/about" element={<AboutPage />} />
              <Route path="/profile" element={<ProfilePage />} />
              <Route path="/sessions" element={<SessionsPage />} />
              <Route path="/sessions/:sessionId" element={<SessionDetailPage />} />
              <Route path="/player/:sessionId" element={<PlayerPage />} />
              <Route path="/journal" element={<JournalPage />} />
              <Route path="/stats" element={<StatsPage />} />
              <Route path="/leaderboard" element={<LeaderboardPage />} />
              <Route path="/blog" element={<BlogPage />} />
              <Route path="/blog/:postId/comments" element={<BlogPostCommentsPage />} />
              <Route path="/games" element={<GamesPage />} />
              <Route path="/monetization" element={<MonetizationPage />} />
              <Route path="/categories" element={<CategoriesPage />} />
              <Route path="/history" element={<HistoryPage />} />
              <Route path="/settings" element={<SettingsPage />} />
              <Route path="/settings/audio-assets" element={<AudioAssetsConfigPage />} />
              <Route path="/redux-example" element={<ReduxExample />} />
              <Route path="*" element={<NotFoundPage />} />
            </Routes>
          </main>

          <NetworkStatusNotifier />
          <UpdateNotification />
          <BottomBar />
        </div>
      </Router>
    );
  }
};

export default App;