# PiKnowKyo - Guide de Déploiement en Production

## 🚀 Application Déployée

**URL de Production :** https://piknowkyo-777.web.app

**Console Firebase :** https://console.firebase.google.com/project/piknowkyo-777/overview

## 📋 Prérequis

- Node.js (version 18+)
- npm ou yarn
- Firebase CLI installé globalement : `npm install -g firebase-tools`
- Accès au projet Firebase `piknowkyo-777`

## 🔧 Configuration

### Variables d'environnement
Assurez-vous que le fichier `.env` contient les bonnes configurations Firebase :

```env
VITE_FIREBASE_API_KEY=your_api_key
VITE_FIREBASE_AUTH_DOMAIN=piknowkyo-777.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=piknowkyo-777
VITE_FIREBASE_STORAGE_BUCKET=piknowkyo-777.appspot.com
VITE_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
VITE_FIREBASE_APP_ID=your_app_id
```

## 🚀 Déploiement

### Méthode 1 : Script automatisé
```bash
npm run deploy
```

### Méthode 2 : Script bash (recommandé)
```bash
./deploy.sh
```

### Méthode 3 : Étapes manuelles
```bash
# 1. Installer les dépendances
npm install

# 2. Construire l'application
npm run build

# 3. Déployer sur Firebase
firebase deploy --only hosting
```

## 📁 Structure de Build

```
dist/
├── index.html              # Point d'entrée principal
├── assets/                 # Fichiers CSS/JS optimisés
├── locales/               # Fichiers de traduction
├── assets/manifests/      # Manifestes des sessions
├── assets/scripts/        # Scripts de méditation
└── assets/audio/          # Fichiers audio (si applicable)
```

## 🔍 Vérification Post-Déploiement

1. **Fonctionnalité de base :**
   - [ ] Page d'accueil se charge
   - [ ] Authentification fonctionne
   - [ ] Navigation entre les pages

2. **Fonctionnalités avancées :**
   - [ ] Sessions de méditation
   - [ ] Journal personnel
   - [ ] Synchronisation offline
   - [ ] Notifications push

3. **Performance :**
   - [ ] Temps de chargement < 3s
   - [ ] Service Worker actif
   - [ ] Cache offline fonctionnel

## 🐛 Dépannage

### Erreurs communes

**Build échoue :**
```bash
# Nettoyer et réinstaller
rm -rf node_modules package-lock.json
npm install
npm run build
```

**Déploiement échoue :**
```bash
# Vérifier l'authentification Firebase
firebase login
firebase projects:list
```

**Application ne se charge pas :**
- Vérifier la console du navigateur pour les erreurs
- Vérifier que les variables d'environnement sont correctes
- Vérifier les règles Firestore et Storage

## 📊 Monitoring

- **Firebase Console :** Monitoring des performances et erreurs
- **Analytics :** Suivi de l'utilisation
- **Crashlytics :** Rapports d'erreurs (si configuré)

## 🔄 Processus de Mise à Jour

1. Développer et tester localement
2. Commit et push vers le repository
3. Exécuter `npm run deploy` ou `./deploy.sh`
4. Vérifier le déploiement sur l'URL de production
5. Tester les fonctionnalités critiques

## 📝 Notes Importantes

- L'application utilise Firebase Hosting avec SPA routing
- Les règles de redirection sont configurées dans `firebase.json`
- Le cache est géré par le Service Worker
- Les sessions sont générées automatiquement lors du build

---

**Dernière mise à jour :** Juin 2025
**Version :** 1.0.0
**Statut :** ✅ En production
