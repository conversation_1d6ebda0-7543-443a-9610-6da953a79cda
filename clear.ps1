# SimplePortKiller.ps1
# Ce script vérifie des ports avec netstat et tue les processus trouvés avec taskkill.
# Ultra-simple, sans fioritures.

# Liste des ports à vérifier et tuer
$portsList = @(9000, 9399, 8080, 5432, 5000)
#declaration de pidtokill
$pidtokill = @()

Write-Host "--- Démarrage de la vérification et du nettoyage des ports ---" -ForegroundColor Cyan
Write-Host "------------------------------------------------------------" -ForegroundColor Cyan

foreach ($port in $portsList) {
    Write-Host "`n--- Port $port ---" -ForegroundColor Yellow

    # 1. Trouver le PID via netstat
    # On filtre sur le port et l'état LISTENING, puis on prend le 5ème token (PID)
    $pidtokill = (netstat -a -n -o | Select-String ":$port" | Select-String "LISTENING" | ForEach-Object {
        $_.Line.Split([char[]]' ', [System.StringSplitOptions]::RemoveEmptyEntries)[4]
    })
    # print le output de netstat
    Write-Host "  Output de netstat: $pidtokill" -ForegroundColor Yellow

    if ($pidtokill) {
        # Si netstat retourne plusieurs lignes (ex: IPv4 et IPv6), on prend le premier PID
        $pidtokill = $pidtokill | Select-Object -First 1

        Write-Host "  Port $port est OUVERT par le PID: $pidtokill. Tentative de tuer..." -ForegroundColor Green
        
        # 2. Tuer le processus avec taskkill
        # /F : Force l'arrêt.
        # /T : Termine le processus et les enfants.
        # 2>&1 | Out-Null : Cache la sortie standard et d'erreur de taskkill
        taskkill /PID $pidtokill /F /T 2>&1 | Out-Null

        # Vérifier le code de retour de taskkill (0 = succès)
        if ($LASTEXITCODE -eq 0) {
            Write-Host "  Commande taskkill exécutée pour PID $pidtokill." -ForegroundColor Yellow
        } else {
            Write-Host "  ÉCHEC de taskkill pour PID $pidtokill (Code: $LASTEXITCODE). Permissions? Processus déjà disparu?" -ForegroundColor Red
        }

        # 3. Revérifier le port après l'opération
        # On refait un netstat pour voir si le port est toujours occupé
        $recheck = (netstat -a -n -o | Select-String ":$port" | Select-String "LISTENING" | Select-Object -First 1)

        if ($recheck) {
            Write-Host "  Statut: Le port $port est TOUJOURS OUVERT." -ForegroundColor Red
        } else {
            Write-Host "  Statut: Le port $port est maintenant FERMÉ." -ForegroundColor Green
        }
    } else {
        Write-Host "  Port $port est FERMÉ (aucun processus n'écoute)." -ForegroundColor DarkGreen
    }
}

Write-Host "`n------------------------------------------------------------" -ForegroundColor Cyan
Write-Host "--- Fin de l'opération. ---" -ForegroundColor Cyan