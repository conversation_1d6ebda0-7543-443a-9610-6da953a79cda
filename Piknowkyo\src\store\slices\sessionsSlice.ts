import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { Session } from '../../models';

export interface SessionsState {
  sessions: Session[];
  currentSession: Session | null;
  isLoading: boolean;
  error: string | null;
  lastSyncTimestamp: number | null;
  pendingChanges: {
    created: Session[];
    updated: Session[];
    deleted: string[];
  };
}

const initialState: SessionsState = {
  sessions: [],
  currentSession: null,
  isLoading: false,
  error: null,
  lastSyncTimestamp: null,
  pendingChanges: {
    created: [],
    updated: [],
    deleted: [],
  },
};

// Actions asynchrones
export const fetchSessions = createAsyncThunk(
  'sessions/fetchSessions',
  async (_, { rejectWithValue }) => {
    try {
      // Pour l'instant, retourner les sessions locales
      const response = await fetch('/assets/manifests/sessions.json');
      if (!response.ok) {
        throw new Error('Failed to fetch sessions');
      }
      const data = await response.json();
      return data.sessions || [];
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

export const createSession = createAsyncThunk(
  'sessions/createSession',
  async (sessionData: Omit<Session, 'id'>, { rejectWithValue }) => {
    try {
      const newSession: Session = {
        ...sessionData,
        id: `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      };

      return newSession;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

const sessionsSlice = createSlice({
  name: 'sessions',
  initialState,
  reducers: {
    setCurrentSession: (state, action: PayloadAction<Session | null>) => {
      state.currentSession = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
    updateLastSyncTimestamp: (state, action: PayloadAction<number>) => {
      state.lastSyncTimestamp = action.payload;
    },
    clearPendingChanges: (state) => {
      state.pendingChanges = {
        created: [],
        updated: [],
        deleted: [],
      };
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch Sessions
      .addCase(fetchSessions.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchSessions.fulfilled, (state, action) => {
        state.isLoading = false;
        state.sessions = action.payload;
        state.lastSyncTimestamp = Date.now();
      })
      .addCase(fetchSessions.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      // Create Session
      .addCase(createSession.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(createSession.fulfilled, (state, action) => {
        state.isLoading = false;
        state.sessions.push(action.payload);
        
        // Si offline, ajouter aux changements en attente
        if (!navigator.onLine) {
          state.pendingChanges.created.push(action.payload);
        }
      })
      .addCase(createSession.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
  },
});

export const {
  setCurrentSession,
  clearError,
  updateLastSyncTimestamp,
  clearPendingChanges,
} = sessionsSlice.actions;

export default sessionsSlice.reducer;
