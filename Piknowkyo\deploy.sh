#!/bin/bash

# PiKnowKyo Production Deployment Script
# This script builds and deploys the application to Firebase Hosting

echo "🚀 Starting PiKnowKyo production deployment..."

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    echo "❌ Error: package.json not found. Please run this script from the project root."
    exit 1
fi

# Install dependencies if node_modules doesn't exist
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies..."
    npm install
fi

# Build the application
echo "🔨 Building application for production..."
npm run build

# Check if build was successful
if [ $? -ne 0 ]; then
    echo "❌ Build failed. Deployment aborted."
    exit 1
fi

# Deploy to Firebase
echo "🌐 Deploying to Firebase Hosting..."
firebase deploy --only hosting

# Check if deployment was successful
if [ $? -eq 0 ]; then
    echo "✅ Deployment successful!"
    echo "🌍 Application is live at: https://piknowkyo-777.web.app"
    echo "📊 Firebase Console: https://console.firebase.google.com/project/piknowkyo-777/overview"
else
    echo "❌ Deployment failed."
    exit 1
fi

echo "🎉 PiKnowKyo deployment completed successfully!"
