// Service Worker Manager for Piknowkyo
// Handles registration, updates, and communication with service worker

export interface ServiceWorkerMessage {
  type: 'SYNC_JOURNAL' | 'SYNC_SESSIONS' | 'UPDATE_AVAILABLE' | 'CACHE_UPDATED';
  timestamp: number;
  data?: any;
}

export interface UpdateInfo {
  version: string;
  timestamp: number;
  hasUpdate: boolean;
}

class ServiceWorkerManager {
  private registration: ServiceWorkerRegistration | null = null;
  private updateCallbacks: ((updateInfo: UpdateInfo) => void)[] = [];
  private syncCallbacks: ((type: string) => void)[] = [];
  private isUpdateAvailable = false;

  constructor() {
    this.setupMessageListener();
  }

  /**
   * Register the service worker
   */
  async register(): Promise<boolean> {
    if (!('serviceWorker' in navigator)) {
      console.warn('[SW Manager] Service workers not supported');
      return false;
    }

    try {
      console.log('[SW Manager] Registering service worker...');
      
      this.registration = await navigator.serviceWorker.register('/sw.js', {
        scope: '/'
      });

      console.log('[SW Manager] Service worker registered successfully');

      // Handle updates
      this.registration.addEventListener('updatefound', () => {
        this.handleUpdateFound();
      });

      // Check for existing updates
      if (this.registration.waiting) {
        this.handleWaitingServiceWorker();
      }

      // Register for background sync if supported
      if ('sync' in window.ServiceWorkerRegistration.prototype) {
        this.setupBackgroundSync();
      }

      return true;
    } catch (error) {
      console.error('[SW Manager] Service worker registration failed:', error);
      return false;
    }
  }

  /**
   * Check for service worker updates
   */
  async checkForUpdates(): Promise<void> {
    if (!this.registration) {
      console.warn('[SW Manager] No service worker registration found');
      return;
    }

    try {
      await this.registration.update();
      console.log('[SW Manager] Update check completed');
    } catch (error) {
      console.error('[SW Manager] Update check failed:', error);
    }
  }

  /**
   * Apply pending service worker update
   */
  async applyUpdate(): Promise<void> {
    if (!this.registration?.waiting) {
      console.warn('[SW Manager] No pending update available');
      return;
    }

    // Send skip waiting message to service worker
    this.registration.waiting.postMessage({ type: 'SKIP_WAITING' });
    
    // Reload the page to activate new service worker
    window.location.reload();
  }

  /**
   * Register background sync for offline actions
   */
  async registerBackgroundSync(tag: string): Promise<boolean> {
    if (!this.registration || !('sync' in window.ServiceWorkerRegistration.prototype)) {
      console.warn('[SW Manager] Background sync not supported');
      return false;
    }

    try {
      await this.registration.sync.register(tag);
      console.log(`[SW Manager] Background sync registered: ${tag}`);
      return true;
    } catch (error) {
      console.error(`[SW Manager] Background sync registration failed: ${tag}`, error);
      return false;
    }
  }

  /**
   * Schedule periodic update checks
   */
  scheduleUpdateChecks(intervalHours: number = 24): void {
    const intervalMs = intervalHours * 60 * 60 * 1000;
    
    setInterval(() => {
      this.checkForUpdates();
    }, intervalMs);

    // Also check on page visibility change
    document.addEventListener('visibilitychange', () => {
      if (!document.hidden) {
        this.checkForUpdates();
      }
    });
  }

  /**
   * Add callback for update notifications
   */
  onUpdateAvailable(callback: (updateInfo: UpdateInfo) => void): void {
    this.updateCallbacks.push(callback);
  }

  /**
   * Add callback for sync events
   */
  onSyncRequired(callback: (type: string) => void): void {
    this.syncCallbacks.push(callback);
  }

  /**
   * Get current update status
   */
  getUpdateStatus(): { hasUpdate: boolean; registration: ServiceWorkerRegistration | null } {
    return {
      hasUpdate: this.isUpdateAvailable,
      registration: this.registration
    };
  }

  /**
   * Clear all caches (for debugging/reset)
   */
  async clearCaches(): Promise<void> {
    if (!('caches' in window)) {
      console.warn('[SW Manager] Cache API not supported');
      return;
    }

    try {
      const cacheNames = await caches.keys();
      await Promise.all(
        cacheNames.map(cacheName => caches.delete(cacheName))
      );
      console.log('[SW Manager] All caches cleared');
    } catch (error) {
      console.error('[SW Manager] Failed to clear caches:', error);
    }
  }

  /**
   * Get cache storage usage
   */
  async getCacheUsage(): Promise<{ used: number; quota: number } | null> {
    if (!('storage' in navigator) || !('estimate' in navigator.storage)) {
      return null;
    }

    try {
      const estimate = await navigator.storage.estimate();
      return {
        used: estimate.usage || 0,
        quota: estimate.quota || 0
      };
    } catch (error) {
      console.error('[SW Manager] Failed to get cache usage:', error);
      return null;
    }
  }

  private setupMessageListener(): void {
    if (!('serviceWorker' in navigator)) return;

    navigator.serviceWorker.addEventListener('message', (event) => {
      const message: ServiceWorkerMessage = event.data;
      
      console.log('[SW Manager] Received message from service worker:', message);

      switch (message.type) {
        case 'UPDATE_AVAILABLE':
          this.handleUpdateMessage(message);
          break;
        case 'SYNC_JOURNAL':
        case 'SYNC_SESSIONS':
          this.handleSyncMessage(message);
          break;
        default:
          console.log('[SW Manager] Unknown message type:', message.type);
      }
    });
  }

  private handleUpdateFound(): void {
    if (!this.registration) return;

    const newWorker = this.registration.installing;
    if (!newWorker) return;

    newWorker.addEventListener('statechange', () => {
      if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
        this.handleWaitingServiceWorker();
      }
    });
  }

  private handleWaitingServiceWorker(): void {
    this.isUpdateAvailable = true;
    
    const updateInfo: UpdateInfo = {
      version: '1.0.0', // This should come from the service worker
      timestamp: Date.now(),
      hasUpdate: true
    };

    this.updateCallbacks.forEach(callback => {
      try {
        callback(updateInfo);
      } catch (error) {
        console.error('[SW Manager] Update callback error:', error);
      }
    });
  }

  private handleUpdateMessage(message: ServiceWorkerMessage): void {
    const updateInfo: UpdateInfo = {
      version: message.data?.version || '1.0.0',
      timestamp: message.timestamp,
      hasUpdate: true
    };

    this.updateCallbacks.forEach(callback => {
      try {
        callback(updateInfo);
      } catch (error) {
        console.error('[SW Manager] Update callback error:', error);
      }
    });
  }

  private handleSyncMessage(message: ServiceWorkerMessage): void {
    const syncType = message.type.replace('SYNC_', '').toLowerCase();
    
    this.syncCallbacks.forEach(callback => {
      try {
        callback(syncType);
      } catch (error) {
        console.error('[SW Manager] Sync callback error:', error);
      }
    });
  }

  private setupBackgroundSync(): void {
    // Register common background sync tags
    const syncTags = ['sync-journal', 'sync-sessions', 'check-updates'];
    
    syncTags.forEach(tag => {
      this.registerBackgroundSync(tag);
    });

    // Schedule periodic update checks
    this.scheduleUpdateChecks(24); // Check every 24 hours
  }
}

// Export singleton instance
export const serviceWorkerManager = new ServiceWorkerManager();

// Export types
export type { ServiceWorkerMessage, UpdateInfo };
export default serviceWorkerManager;
