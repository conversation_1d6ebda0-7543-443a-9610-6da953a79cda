// src/models.ts
export interface SessionAudioConfig {
  music?: {
    url: string;
    volume?: number;
  };
  ambient?: {
    url: string;
    volume?: number;
  };
  binaural?: {
    volume?: number; // Volume commun
    
    // Pour les binauraux GÉNÉRÉS PAR L'APPLICATION
    baseFreq?: number;  // Fréquence de base (ex: pour l'oreille gauche)
    beatFreq?: number;  // Fréquence du battement désiré (différence entre les deux oreilles)
                        // targetFreq (pour l'oreille droite) sera généralement baseFreq + beatFreq ou baseFreq - beatFreq
    
  };
  voice?: {
    volume?: number;
    gender?: 'masculine' | 'feminine' | 'neutral';
  };
  enableMusic?: boolean;
  enableAmbient?: boolean;
  enableBinaural?: boolean; // Ce toggle est important pour savoir si on doit générer/jouer les binauraux
}

// ... (Le reste de vos interfaces : AudioAsset, ScriptLine, Session, etc.)
// Assurez-vous qu'elles sont à jour avec les modifications précédentes si besoin.
export interface AudioAsset {
  id: string;
  name: string;
  url: string;
  type: 'music' | 'ambient';
  isUserUploaded?: boolean;
  userId?: string; // ID de l'utilisateur qui a uploadé (si applicable)
  storagePath?: string;
  size?: number; // Taille du fichier en octets (optionnel)
  createdAt?: Date; // Date de création/upload (optionnel)
}

export interface ScriptLine {
  text: string;
  duration?: number; 
  pause?: number;    
  speaker?: string;  
  rate?: number;     
  pitch?: number;    
}

import { Language } from './LangProvider'; // Assurez-vous que ce chemin est correct

export interface Session {
  id: string;
  title: string;
  description: string;
  type: string; 
  category: string; 
  language: Language; 
  imageUrl?: string; 
  durationMinutes?: number; 
  estimatedDuration?: number; 
  rating?: number;
  tags?: string[];
  benefits?: string[];
  comments?: string[]; 
  audio?: SessionAudioConfig; 
  script?: ScriptLine[]; 
}

export interface AudioManifest {
  musics: AudioAsset[];
  ambiants: AudioAsset[];
}

export type JournalEntry = {
  id: string;
  sessionId: string;
  date: string;
  note: string;
  mood: 'happy' | 'neutral' | 'sad' | 'energized' | 'relaxed';
};

// AJOUT DE L'INTERFACE MANQUANTE ET SON EXPORTATION
export interface SessionManifestEntry {
  id: string;
  title: string;
  type: string;
  estimatedDuration: number;
  tags: string[];        // Doit être string[] comme dans le script de build
  imageUrl?: string;
  isPremium?: boolean;
  // Ajoutez d'autres champs si votre script build-sessions.js les inclut dans le manifeste
};
