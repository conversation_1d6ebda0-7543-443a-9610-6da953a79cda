rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {

    // --- RÈGLES UTILISATEURS ET LEURS DONNÉES PRIVÉES ---
    match /users/{userId} {
      // Un utilisateur peut créer son propre document de profil
      allow create: if request.auth.uid != null;
      // Seul le propriétaire peut lire, mettre à jour, supprimer son propre document de profil
      allow read, update, delete: if request.auth.uid == userId;

      // Accès aux sous-collections privées de l'utilisateur (journal, historique, préférences)
      match /{document=**} {
        allow read, write: if request.auth.uid == userId;
      }
    }

    // --- RÈGLES POUR LES DONNÉES PUBLIQUES (après authentification) ---

    // Sessions: Lisibles par TOUT utilisateur AUTHENTIFIÉ
    // Écriture réservée aux admins (si la revendication custom token 'isAdmin' est présente)
    match /sessions/{sessionId} {
      allow read: if request.auth.uid != null;
      allow write: if request.auth.token != null && request.auth.token.isAdmin == true; // Vérifiez l'existence du token avant d'accéder à .isAdmin
    }

    // Articles de blog: Lisibles par TOUT utilisateur AUTHENTIFIÉ
    // Si la page blog est publique avant connexion, changer à `if true;`
    match /blogPosts/{postId} {
      allow read: if request.auth.uid != null;
      // allow write: if request.auth.token != null && request.auth.token.isAdmin == true;
    }

    // Catégories: Lisibles par TOUT utilisateur AUTHENTIFIÉ
    // Si la page catégories est publique avant connexion, changer à `if true;`
    match /categories/{categoryId} {
      allow read: if request.auth.uid != null;
      // allow write: if request.auth.token != null && request.auth.token.isAdmin == true;
    }

    // Actifs audio (music, ambient, etc.): Lisibles par TOUT utilisateur AUTHENTIFIÉ
    // Si certains actifs sont publics, ajustez.
    match /audioAssets/{assetId} {
      allow read: if request.auth.uid != null;
      allow write: if request.auth.token != null && request.auth.token.isAdmin == true;
    }

    // Configuration des prix (collection 'config', document 'pricing'):
    // Le document 'pricing' est lisible par TOUT utilisateur AUTHENTIFIÉ.
    match /config/{documentId} {
      allow read: if documentId == 'pricing' && request.auth.uid != null;
      // allow write: if request.auth.token != null && request.auth.token.isAdmin == true;
    }

    // --- RÈGLE TRÈS PERMISSIVE POUR TOUT LE RESTE (POUR DÉVELOPPEMENT SEULEMENT) ---
    // Cette règle attrape tout document non couvert par les règles spécifiques ci-dessus.
    // Elle est généralement utile pour le débogage initial et à NE PAS UTILISER EN PRODUCTION.
    // Si vous ne l'utilisez pas, assurez-vous que CHAQUE collection a sa propre règle explicite.
    match /{path=**} {
        allow read, write: if false; // Tout le reste est interdit par défaut
    }
  }
}