rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function isOwner(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }
    
    function isValidUser() {
      return isAuthenticated() && 
             request.auth.token.email_verified == true;
    }
    
    // User profiles - users can only access their own profile
    match /userProfiles/{userId} {
      allow read, write: if isOwner(userId) && isValidUser();
    }
    
    // Journal entries - users can only access their own entries
    match /journal/{userId}/entries/{entryId} {
      allow read, write: if isOwner(userId) && isValidUser();
    }
    
    // Session history - users can only access their own history
    match /sessionHistory/{userId}/sessions/{sessionId} {
      allow read, write: if isOwner(userId) && isValidUser();
    }
    
    // Subscriptions - users can only read their own subscription data
    match /subscriptions/{userId} {
      allow read: if isOwner(userId) && isValidUser();
      allow write: if false; // Only server can write subscription data
    }
    
    // Public sessions/scripts - read-only for all authenticated users
    match /sessions/{sessionId} {
      allow read: if isValidUser();
      allow write: if false; // Only admins can modify sessions
    }
    
    match /scripts/{language}/{scriptId} {
      allow read: if isValidUser();
      allow write: if false; // Only admins can modify scripts
    }
    
    // Game scores - users can read all, write their own
    match /gameScores/{userId}/scores/{scoreId} {
      allow read: if isValidUser();
      allow create, update: if isOwner(userId) && isValidUser();
      allow delete: if isOwner(userId) && isValidUser();
    }
    
    // Deny all other access
    match /{document=**} {
      allow read, write: if false;
    }
  }
}
