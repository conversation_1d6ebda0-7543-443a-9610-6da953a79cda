rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function isOwner(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }
    
    function isValidUser() {
      return isAuthenticated() && 
             request.auth.token.email_verified == true;
    }
    
    function hasValidSubscription() {
      return isAuthenticated() && 
             (request.auth.token.stripeRole == 'premium' || 
              request.auth.token.trialActive == true);
    }
    
    function isValidAudioFile() {
      return resource.contentType.matches('audio/.*') &&
             resource.size < 50 * 1024 * 1024; // Max 50MB
    }
    
    function isValidImageFile() {
      return resource.contentType.matches('image/.*') &&
             resource.size < 10 * 1024 * 1024; // Max 10MB
    }
    
    // User profile images
    match /users/{userId}/profile/{fileName} {
      allow read: if isValidUser();
      allow write: if isOwner(userId) && 
                      isValidUser() && 
                      isValidImageFile();
    }
    
    // User uploaded audio files (premium feature)
    match /users/{userId}/audio/{fileName} {
      allow read: if isOwner(userId) && isValidUser();
      allow write: if isOwner(userId) && 
                      isValidUser() && 
                      hasValidSubscription() && 
                      isValidAudioFile();
    }
    
    // Public assets (read-only)
    match /public/{allPaths=**} {
      allow read: if true;
      allow write: if false; // Only admins can upload public assets
    }
    
    // App assets (read-only for authenticated users)
    match /assets/{allPaths=**} {
      allow read: if isValidUser();
      allow write: if false; // Only admins can upload app assets
    }
    
    // Deny all other access
    match /{allPaths=**} {
      allow read, write: if false;
    }
  }
}
