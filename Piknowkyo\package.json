{"name": "piknowkyo", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "npm run build:manifests && vite", "start": "npm run build:manifests && vite", "build": "npm run build:manifests && tsc -b && vite build", "test": "vitest", "lint": "eslint .", "preview": "vite preview", "deploy": "npm run build && firebase deploy", "build:manifests": "node scripts/build-sessions.js", "build:audio-assets": "node scripts/build-audio-assets-manifests.js"}, "dependencies": {"@capacitor/cli": "^7.2.0", "@capacitor/core": "^7.2.0", "@capacitor/push-notifications": "^7.0.1", "@reduxjs/toolkit": "^2.8.2", "firebase": "^11.8.1", "fs-extra": "^11.3.0", "i18next": "^25.2.0", "i18next-browser-languagedetector": "^8.1.0", "i18next-http-backend": "^3.0.2", "polished": "^4.3.1", "react": "^19.1.0", "react-dom": "^19.1.0", "react-i18next": "^15.5.1", "react-icons": "^5.1.0", "react-redux": "^9.2.0", "react-router-dom": "^7.6.0", "redux-persist": "^6.0.0", "redux-persist-transform-encrypt": "^5.1.1", "styled-components": "^6.1.18", "web-vitals": "^2.1.4"}, "devDependencies": {"@eslint/js": "^9.25.0", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^22.0.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@types/styled-components": "^5.1.34", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}}