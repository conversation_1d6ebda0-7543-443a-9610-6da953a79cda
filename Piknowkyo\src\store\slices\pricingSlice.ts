// src/store/slices/pricingSlice.ts
import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { doc, getDoc } from 'firebase/firestore';
import { db } from '../../firebase'; // Assurez-vous que 'db' est exporté de votre fichier firebase.ts

// Interface pour la structure des données de prix que vous attendez de Firebase
// Basé sur votre capture d'écran: config/pricing -> premium document
export interface PremiumConfigFromFirestore {
  features: string[]; // Tableau de chaînes pour les fonctionnalités
  premiumCurrency: string;
  premiumPrice: number; // Le prix de base (mensuel, dans votre cas 8.99)
  trialDays: number;
  // Si vous avez d'autres champs dans le document 'premium', ajoutez-les ici
  // Par exemple: yearlyDiscountPercentage?: number;
}

// Interface pour les plans de tarification finaux que l'application va utiliser
// C'est ce qui sera construit à partir de PremiumConfigFromFirestore
export interface PricingPlan {
  id: string;
  name: string;
  price: number;
  currency: string;
  interval: 'month' | 'year';
  features: string[];
  stripePriceId?: string; // Ces IDs proviennent toujours des variables d'environnement
  paddlePriceId?: string;
}

interface PricingState {
  premiumConfig: PremiumConfigFromFirestore | null; // Les données brutes de Firestore
  pricingPlans: PricingPlan[]; // Les plans de tarification construits
  isLoading: boolean;
  error: string | null;
}

const initialState: PricingState = {
  premiumConfig: null,
  pricingPlans: [],
  isLoading: false,
  error: null,
};

// Thunk pour charger la configuration des prix depuis Firebase
export const fetchPricingConfig = createAsyncThunk(
  'pricing/fetchPricingConfig',
  async (_, { rejectWithValue }) => {
    try {
      // Chemin vers votre document de prix dans Firestore
      const docRef = doc(db, 'config', 'pricing');
      const docSnap = await getDoc(docRef);

      if (docSnap.exists()) {
        const data = docSnap.data();
        // Assurez-vous que le champ 'premium' existe et a le bon format
        if (data.premium) {
          return data.premium as PremiumConfigFromFirestore;
        } else {
          return rejectWithValue('Champ "premium" non trouvé dans le document de prix Firestore.');
        }
      } else {
        return rejectWithValue('Document de prix (config/pricing) non trouvé dans Firestore.');
      }
    } catch (err: any) {
      console.error("Erreur lors du chargement de la configuration de prix:", err);
      return rejectWithValue(err.message || 'Échec du chargement de la configuration de prix.');
    }
  }
);

const pricingSlice = createSlice({
  name: 'pricing',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(fetchPricingConfig.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchPricingConfig.fulfilled, (state, action) => {
        state.isLoading = false;
        state.premiumConfig = action.payload;

        // Construire les plans de tarification (mensuel et annuel)
        const basePrice = action.payload.premiumPrice;
        const currency = action.payload.premiumCurrency || 'USD'; // Par défaut USD si non spécifié

        // Calcul du prix annuel avec un rabais (ex: 2 mois gratuits sur 12 = payer 10 mois)
        // Vous pouvez ajuster cette logique selon vos besoins de rabais.
        const yearlyPrice = Math.round((basePrice * 10) * 100) / 100; // Arrondi à 2 décimales pour les cents
        const yearlySavingsPercentage = Math.round(((basePrice * 12 - yearlyPrice) / (basePrice * 12)) * 100);

        state.pricingPlans = [
          {
            id: 'premium_monthly',
            name: 'Premium Monthly',
            price: basePrice,
            currency: currency,
            interval: 'month',
            features: action.payload.features,
            // Les IDs de prix Stripe/Paddle restent des variables d'environnement
            stripePriceId: import.meta.env.VITE_STRIPE_PRICE_MONTHLY,
            paddlePriceId: import.meta.env.VITE_PADDLE_PRICE_MONTHLY,
          },
          {
            id: 'premium_yearly',
            name: 'Premium Yearly',
            price: yearlyPrice,
            currency: currency,
            interval: 'year',
            // Ajoutez la fonctionnalité de "X mois gratuits" dynamiquement si elle n'est pas déjà dans Firestore
            features: [...action.payload.features, `${(12 - 10)} mois gratuits (économisez ${yearlySavingsPercentage}%)`],
            stripePriceId: import.meta.env.VITE_STRIPE_PRICE_YEARLY,
            paddlePriceId: import.meta.env.VITE_PADDLE_PRICE_YEARLY,
          }
        ];
      })
      .addCase(fetchPricingConfig.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
        state.premiumConfig = null;
        state.pricingPlans = []; // Vider les plans en cas d'erreur
      });
  },
});

export default pricingSlice.reducer;