// src/services/tts.ts

export type TTSProvider = 
  | 'browser' 
  | 'piper' 
  | 'ttswebui' 
  | 'openvoice' 
  | 'parlertts' 
  | 'murf' // Ajout de Murf qui était dans les fonctions mais pas dans le type
  | 'apicustom' // Ajout de ApiCustom
  | 'cloud'; // Ajout de Cloud

export interface TTSConfig {
  volume?: number;        // 0.0 to 1.0
  rate?: number;          // 0.1 to 10
  pitch?: number;         // 0 to 2
  signal?: AbortSignal;   // Pour annuler la lecture
  // Ajoutez d'autres options de config spécifiques à certains providers si nécessaire
  apiUrl?: string;        // Pour 'apicustom'
  apiKey?: string;        // Pour 'apicustom' ou 'cloud'
}

// --- Fonctions spécifiques aux providers (placeholders pour la plupart) ---

export async function ttsPiper(text: string, voice: string, lang: string, config: TTSConfig): Promise<void> {
  console.log(`TTS Piper: "${text}", Voice: ${voice}, Lang: ${lang}, Config:`, config);
  // Ici, vous appelleriez votre serveur local Piper ou une implémentation WASM.
  // Pour la démo, on simule une lecture.
  // const audio = new Audio(`/audio/piper_placeholder.mp3`);
  // audio.volume = config.volume ?? 1;
  // await audio.play();
  return Promise.resolve(); // Simule la fin de la lecture
}

export async function ttsWebUI(text: string, voice: string, lang: string, config: TTSConfig): Promise<void> {
  console.log(`TTS WebUI: "${text}", Voice: ${voice}, Lang: ${lang}, Config:`, config);
  // Exemple d'appel API (à adapter)
  // const response = await fetch('http://localhost:5000/api/tts', {
  //   method: 'POST',
  //   headers: { 'Content-Type': 'application/json' },
  //   body: JSON.stringify({ text, speaker_wav: voice, language: lang, ... }),
  //   signal: config.signal,
  // });
  // if (!response.ok) throw new Error('TTS WebUI request failed');
  // const audioBlob = await response.blob();
  // const audioUrl = URL.createObjectURL(audioBlob);
  // const audio = new Audio(audioUrl);
  // audio.volume = config.volume ?? 1;
  // await audio.play();
  // URL.revokeObjectURL(audioUrl);
  return Promise.resolve();
}

// ... autres placeholders similaires pour openvoice, parlertts, murf, apicustom, cloud ...
export async function ttsOpenVoice(text: string, voice: string, lang: string, config: TTSConfig): Promise<void> {
  console.log(`TTS OpenVoice: "${text}", Voice: ${voice}, Lang: ${lang}, Config:`, config);
  return Promise.resolve();
}
export async function ttsMurf(text: string, voice: string, lang: string, config: TTSConfig): Promise<void> {
  console.log(`TTS Murf: "${text}", Voice: ${voice}, Lang: ${lang}, Config:`, config);
  return Promise.resolve();
}
export async function ttsParlerTTS(text: string, voice: string, lang: string, config: TTSConfig): Promise<void> {
  console.log(`TTS ParlerTTS: "${text}", Voice: ${voice}, Lang: ${lang}, Config:`, config);
  return Promise.resolve();
}
export async function ttsApiCustom(text: string, voice: string, lang: string, config: TTSConfig): Promise<void> {
  console.log(`TTS ApiCustom: "${text}", URL: ${config.apiUrl}, Voice: ${voice}, Config:`, config);
  return Promise.resolve();
}
export async function ttsCloud(text: string, voice: string, lang: string, config: TTSConfig): Promise<void> {
  console.log(`TTS Cloud: "${text}", Voice: ${voice}, Lang: ${lang}, Config:`, config);
  return Promise.resolve();
}


// --- Synthèse vocale du navigateur (Web Speech API) ---
let currentUtterance: SpeechSynthesisUtterance | null = null;

export async function ttsBrowser(
  text: string, 
  voiceName: string, // Nom de la voix ou 'auto'
  lang: string, 
  config: TTSConfig
): Promise<void> {
  return new Promise((resolve, reject) => {
    if (!window.speechSynthesis) {
      reject(new Error("Web Speech API (SpeechSynthesis) n'est pas supportée par ce navigateur."));
      return;
    }

    // Si une lecture est en cours et qu'un signal d'annulation est reçu, l'arrêter.
    // Cela ne fonctionnera que si le même AbortController est utilisé.
    // ttsStop gère l'annulation de manière plus directe.
    if (config.signal?.aborted) {
      // console.log("ttsBrowser: Lecture annulée par signal avant le démarrage.");
      reject(new DOMException('Lecture annulée', 'AbortError'));
      return;
    }

    // Annuler la parole précédente si elle est en cours
    if (window.speechSynthesis.speaking) {
      window.speechSynthesis.cancel();
    }

    const utter = new SpeechSynthesisUtterance(text);
    currentUtterance = utter; // Garder une référence

    utter.lang = lang;
    utter.volume = config.volume ?? 1; // Volume de 0 à 1
    utter.rate = config.rate ?? 1;     // Vitesse de 0.1 à 10
    utter.pitch = config.pitch ?? 1;   // Hauteur de 0 à 2

    // Sélection de la voix
    if (voiceName && voiceName !== 'auto') {
      const voices = window.speechSynthesis.getVoices();
      // Il faut parfois attendre que les voix soient chargées, surtout au premier appel
      if (voices.length === 0) {
        window.speechSynthesis.onvoiceschanged = () => {
          const updatedVoices = window.speechSynthesis.getVoices();
          const selectedVoice = updatedVoices.find(v => v.name === voiceName && v.lang.startsWith(lang));
          if (selectedVoice) utter.voice = selectedVoice;
          else {
            const langVoice = updatedVoices.find(v => v.lang.startsWith(lang));
            if (langVoice) utter.voice = langVoice; // Fallback sur la première voix de la langue
          }
          // Nettoyer l'événement une fois les voix chargées
          window.speechSynthesis.onvoiceschanged = null; 
        };
      } else {
          const selectedVoice = voices.find(v => v.name === voiceName && v.lang.startsWith(lang));
          if (selectedVoice) utter.voice = selectedVoice;
          else {
            const langVoice = voices.find(v => v.lang.startsWith(lang));
            if (langVoice) utter.voice = langVoice;
          }
      }
    } else {
        // Si 'auto' ou pas de voix spécifiée, essayer de trouver une voix pour la langue
        const voices = window.speechSynthesis.getVoices();
        if (voices.length > 0) {
            const langVoice = voices.find(v => v.lang.startsWith(lang));
            if (langVoice) utter.voice = langVoice;
        } else {
             window.speechSynthesis.onvoiceschanged = () => {
                const updatedVoices = window.speechSynthesis.getVoices();
                const langVoice = updatedVoices.find(v => v.lang.startsWith(lang));
                if (langVoice) utter.voice = langVoice;
                window.speechSynthesis.onvoiceschanged = null;
            };
        }
    }
    
    const onEnd = () => {
      // console.log("ttsBrowser: Fin de la lecture.");
      if (currentUtterance === utter) currentUtterance = null;
      resolve();
    };

    const onError = (event: SpeechSynthesisErrorEvent) => {
      //console.error("ttsBrowser: Erreur de synthèse vocale:", event.error, event);
      if (currentUtterance === utter) currentUtterance = null;
      // Ne pas rejeter si c'est une annulation, qui est gérée par le signal
      if (event.error !== 'canceled' && event.error !== 'interrupted') {
        reject(new Error(`Erreur de synthèse vocale: ${event.error}`));
      } else {
        resolve(); // Résoudre en cas d'annulation pour ne pas bloquer la chaîne de promesses
      }
    };

    utter.onend = onEnd;
    utter.onerror = onError;

    // Gérer l'annulation via AbortSignal
    const signalListener = () => {
      // console.log("ttsBrowser: Signal d'annulation reçu.");
      window.speechSynthesis.cancel(); // Annule la parole en cours et vide la file d'attente
      if (currentUtterance === utter) currentUtterance = null;
      // La promesse sera rejetée par l'événement 'error' avec 'canceled' ou résolue par onend
      // Nous devons nous assurer qu'elle se résout/rejette pour que le code appelant continue.
      // Si onerror n'est pas appelé à temps, on peut rejeter ici.
      // Cependant, `cancel()` devrait déclencher `onerror` ou `onend`.
      // Pour plus de sûreté, on peut aussi rejeter ici si le signal est déjà émis.
      // reject(new DOMException('Lecture annulée par signal', 'AbortError'));
    };

    if (config.signal) {
      if (config.signal.aborted) { // Vérifier si déjà annulé
        signalListener(); // Appeler pour nettoyer et rejeter
        return; // Important pour ne pas appeler speak
      }
      config.signal.addEventListener('abort', signalListener, { once: true });
      utter.onend = () => { config.signal?.removeEventListener('abort', signalListener); onEnd(); };
      utter.onerror = (event) => { config.signal?.removeEventListener('abort', signalListener); onError(event); };
    }
    
    // Lancer la parole
    window.speechSynthesis.speak(utter);
  });
}


// --- Fonction principale pour jouer le TTS ---
export async function ttsPlay(
  provider: TTSProvider,
  text: string,
  voice: string, // Nom de la voix ou 'auto'
  lang: string,
  config: TTSConfig = {} // Configuration optionnelle avec des valeurs par défaut
): Promise<void> { // La plupart des implémentations ne retourneront pas d'élément audio directement
  switch (provider) {
    case 'piper':
      return ttsPiper(text, voice, lang, config);
    case 'ttswebui':
      return ttsWebUI(text, voice, lang, config);
    case 'openvoice':
      return ttsOpenVoice(text, voice, lang, config);
    case 'parlertts':
      return ttsParlerTTS(text, voice, lang, config);
    case 'murf':
        return ttsMurf(text, voice, lang, config);
    case 'apicustom':
        return ttsApiCustom(text, voice, lang, config);
    case 'cloud':
        return ttsCloud(text, voice, lang, config);
    case 'browser':
    default:
      return ttsBrowser(text, voice, lang, config);
  }
}

// --- Fonction pour arrêter la lecture TTS (principalement pour le navigateur) ---
export function ttsStop(abortController?: AbortController | null): void {
  // Pour le TTS du navigateur
  if (window.speechSynthesis && window.speechSynthesis.speaking) {
    // console.log("ttsStop: Arrêt de la synthèse vocale du navigateur.");
    window.speechSynthesis.cancel(); // Annule la parole actuelle et vide la file d'attente
  }
  if (currentUtterance) { // Réinitialiser la référence
    currentUtterance = null;
  }

  // Pour les autres providers qui pourraient utiliser un AbortController
  if (abortController && !abortController.signal.aborted) {
    // console.log("ttsStop: Annulation via AbortController.");
    abortController.abort();
  }
  
  // Si d'autres providers ont des méthodes d'arrêt spécifiques, ajoutez-les ici
}