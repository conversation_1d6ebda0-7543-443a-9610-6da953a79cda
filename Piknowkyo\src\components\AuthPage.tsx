// src/components/AuthPage.tsx
import React, { useState } from 'react';
import { useTheme } from '../ThemeProvider';
import { useTranslation } from 'react-i18next'; // Importez useTranslation
import { FiSun, FiMoon } from 'react-icons/fi';

import Login from './Login';
import SignupForm from './SignupForm';

import {
  AuthPageContainer,
  AuthCard,
  HeaderActions,
  ThemeToggleButton,
  AppLogo,
  Title,
  Subtitle
} from './common/AuthStyles';

const AuthPage: React.FC = () => {
  const [currentForm, setCurrentForm] = useState<'login' | 'signup'>('login');
  const { darkMode, toggleTheme } = useTheme();
  const { t } = useTranslation(); // Initialisez useTranslation

  const handleToggleForm = (formType: 'login' | 'signup') => {
    setCurrentForm(formType);
  };

  return (
    <AuthPageContainer>
      <AuthCard>
        <HeaderActions>
          <ThemeToggleButton
            onClick={toggleTheme}
            aria-label={darkMode ? t('app.theme_light') : t('app.theme_dark')} // Traduction ici
          >
            {darkMode ? <FiSun size={20} /> : <FiMoon size={20} />}
          </ThemeToggleButton>
        </HeaderActions>

        <AppLogo src="/logo192.png" alt={t('app.logo_alt')} /> {/* Traduction ici */}
        <Title>{t('app.name')}</Title> {/* Utilise la clé existante 'app.name' */}
        <Subtitle>
          {currentForm === 'login' ? t('auth.login.subtitle') : t('auth.signup.subtitle')} {/* Traduction ici */}
        </Subtitle>

        {currentForm === 'login' ? (
          <Login onToggleForm={handleToggleForm} />
        ) : (
          <SignupForm onToggleForm={handleToggleForm} />
        )}
      </AuthCard>
    </AuthPageContainer>
  );
};

export default AuthPage;