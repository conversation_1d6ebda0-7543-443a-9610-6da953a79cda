// Stripe Service for Piknowkyo
// Handles payment processing and subscription management

import { loadStripe, Stripe } from '@stripe/stripe-js';
import { db, auth } from '../firebase';
import { collection, addDoc, doc, onSnapshot } from 'firebase/firestore';

export interface PaymentIntent {
  id: string;
  amount: number;
  currency: string;
  status: string;
  client_secret: string;
}

export interface SubscriptionData {
  priceId: string;
  customerId?: string;
  successUrl: string;
  cancelUrl: string;
}

export interface CheckoutSession {
  id: string;
  url: string;
  status: string;
}

class StripeService {
  private stripe: Stripe | null = null;
  private isInitialized = false;

  constructor() {
    this.initialize();
  }

  /**
   * Initialize Stripe
   */
  private async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      const publishableKey = import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY;

      if (!publishableKey) {
        console.warn('[Stripe] Publishable key not found, using test mode');
        // Use test key for development
        this.stripe = await loadStripe('pk_test_51234567890abcdef');
      } else {
        this.stripe = await loadStripe(publishableKey);
      }

      this.isInitialized = true;
      console.log('[Stripe] Initialized successfully');
    } catch (error) {
      console.error('[Stripe] Initialization failed:', error);
    }
  }

  /**
   * Create checkout session for subscription
   */
  async createCheckoutSession(data: SubscriptionData): Promise<CheckoutSession> {
    if (!auth.currentUser) {
      throw new Error('User not authenticated');
    }

    try {
      // Create checkout session via Firebase Function
      const checkoutSessionRef = collection(
        db,
        `users/${auth.currentUser.uid}/checkout_sessions`
      );

      const docRef = await addDoc(checkoutSessionRef, {
        price: data.priceId,
        success_url: data.successUrl,
        cancel_url: data.cancelUrl,
        mode: 'subscription',
        allow_promotion_codes: true,
        metadata: {
          userId: auth.currentUser.uid,
          timestamp: Date.now()
        }
      });

      // Wait for the checkout session to be created by the Firebase Function
      return new Promise((resolve, reject) => {
        const unsubscribe = onSnapshot(docRef, (snap) => {
          const data = snap.data();

          if (data?.error) {
            unsubscribe();
            reject(new Error(data.error.message));
          }

          if (data?.url) {
            unsubscribe();
            resolve({
              id: snap.id,
              url: data.url,
              status: 'created'
            });
          }
        });

        // Timeout after 10 seconds
        setTimeout(() => {
          unsubscribe();
          reject(new Error('Checkout session creation timeout'));
        }, 10000);
      });

    } catch (error) {
      console.error('[Stripe] Failed to create checkout session:', error);
      throw error;
    }
  }

  /**
   * Create customer portal session
   */
  async createPortalSession(returnUrl: string): Promise<{ url: string }> {
    if (!auth.currentUser) {
      throw new Error('User not authenticated');
    }

    try {
      const portalSessionRef = collection(
        db,
        `users/${auth.currentUser.uid}/portal_sessions`
      );

      const docRef = await addDoc(portalSessionRef, {
        return_url: returnUrl,
        metadata: {
          userId: auth.currentUser.uid,
          timestamp: Date.now()
        }
      });

      // Wait for the portal session to be created
      return new Promise((resolve, reject) => {
        const unsubscribe = onSnapshot(docRef, (snap) => {
          const data = snap.data();

          if (data?.error) {
            unsubscribe();
            reject(new Error(data.error.message));
          }

          if (data?.url) {
            unsubscribe();
            resolve({ url: data.url });
          }
        });

        // Timeout after 10 seconds
        setTimeout(() => {
          unsubscribe();
          reject(new Error('Portal session creation timeout'));
        }, 10000);
      });

    } catch (error) {
      console.error('[Stripe] Failed to create portal session:', error);
      throw error;
    }
  }

  /**
   * Get available pricing plans
   */
  getPricingPlans() {
    return [
      {
        id: 'premium_monthly',
        name: 'Premium Monthly',
        price: 9.99,
        currency: 'USD',
        interval: 'month',
        priceId: import.meta.env.VITE_STRIPE_PRICE_MONTHLY || 'price_test_monthly',
        features: [
          'All meditation sessions',
          'Binaural beats & ambient sounds',
          'Mindfulness games',
          'Custom audio upload',
          'Advanced analytics',
          'No ads',
          'Cloud TTS voices'
        ]
      },
      {
        id: 'premium_yearly',
        name: 'Premium Yearly',
        price: 99.99,
        currency: 'USD',
        interval: 'year',
        priceId: import.meta.env.VITE_STRIPE_PRICE_YEARLY || 'price_test_yearly',
        features: [
          'All meditation sessions',
          'Binaural beats & ambient sounds',
          'Mindfulness games',
          'Custom audio upload',
          'Advanced analytics',
          'No ads',
          'Cloud TTS voices',
          '2 months free (save 17%)'
        ]
      }
    ];
  }

  /**
   * Check if Stripe is available
   */
  isAvailable(): boolean {
    return this.isInitialized && this.stripe !== null;
  }

  /**
   * Get Stripe instance
   */
  getStripe(): Stripe | null {
    return this.stripe;
  }
}

// Export singleton instance
export const stripeService = new StripeService();
export default stripeService;